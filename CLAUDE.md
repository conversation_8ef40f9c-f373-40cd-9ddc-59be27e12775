# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Spring Boot application called **TgExplain** that parses Telegram chat export HTML files and stores the chat messages in a MySQL database. The application processes group chat messages, including text content, media, reactions, forwarded messages, and user information.

## Build System and Common Commands

### Maven Commands
```bash
# Build the project
./mvnw clean compile

# Run tests
./mvnw test

# Run the application
./mvnw spring-boot:run

# Package the application
./mvnw clean package

# Skip tests during build
./mvnw clean package -DskipTests
```

### Running the Application
- Main class: `xiaowu.tgexplain.TgExplainApplication`
- Default port: 8080 (configurable via `application.properties`)
- Database: MySQL (requires configuration in `application.properties`)

## Architecture Overview

### Core Components

1. **HTML Parsing Service** (`HtmlParsingService`)
   - Parses Telegram HTML export files using JSoup
   - Extracts messages, user information, media, reactions, and forwarded content
   - Handles different message types: service messages, default messages, and joined messages

2. **Database Storage Service** (`DatabaseStorageService`)
   - Stores parsed chat data in MySQL database
   - Manages transactions and prevents duplicate message imports
   - Handles user caching and relationship management

3. **Entity Layer** (`entity/`)
   - `ChatEntity`: Represents chat groups
   - `MessageEntity`: Stores individual messages with all metadata
   - `ChatUserEntity`: User information and profiles
   - `ReactionEntity`: Message reactions/emojis

4. **Model Layer** (`model/`)
   - DTOs for data transfer between parsing and storage layers
   - `ChatMessage`, `Message`, `Reaction` classes

5. **Repository Layer** (`repository/`)
   - JPA repositories for database operations
   - Custom queries for message and user lookups

### Key Features

- **Message Type Handling**: Processes service messages (date separators, pins), default messages, and joined messages (consecutive messages from same user)
- **Media Support**: Handles images, thumbnails, and placeholder media content
- **Forwarded Messages**: Stores forwarded content as JSON in the database
- **User Management**: Tracks user profiles with initials and color classes
- **Reaction System**: Stores emoji reactions with user associations
- **Duplicate Prevention**: Prevents re-importing existing messages using business IDs

### Database Schema

- Messages are identified by `businessId` (e.g., "message9145")
- Relationships: Chat → Messages → Users/Reactions
- Forwarded messages stored as JSON strings
- Uses JPA annotations with proper indexing

### Dependencies

Key libraries used:
- **Spring Boot 3.5.3**: Framework foundation
- **JSoup 1.17.2**: HTML parsing for Telegram exports
- **MySQL Connector**: Database connectivity
- **JPA/Hibernate**: ORM for database operations
- **Lombok**: Reduces boilerplate code
- **Thymeleaf**: Template engine (if web UI is added)

### Development Notes

- The application expects Telegram HTML export files as input
- Database configuration must be set in `application.properties`
- Uses `@Transactional` for atomic database operations
- Implements proper error handling for JSON serialization
- Supports both individual and batch message processing

### File Structure

```
TgExplain/
├── src/main/java/xiaowu/tgexplain/
│   ├── TgExplainApplication.java (Main application class)
│   ├── entity/ (JPA entities)
│   ├── model/ (DTOs)
│   ├── repository/ (Data access layer)
│   └── service/ (Business logic)
├── src/main/resources/
│   ├── application.properties (Configuration)
│   ├── static/ (Static web resources)
│   └── templates/ (Thymeleaf templates)
└── src/test/java/ (Test classes)
```

The `chats/` directory contains sample Telegram HTML export files that the application is designed to process.