<script setup lang="ts">
import type { ChatMessageDto } from '../api/chat'

interface Props {
  group: string | null
  messages: ChatMessageDto[]
  isLoading: boolean
}

defineProps<Props>()
</script>

<template>
  <div class="bg-white rounded-xl shadow-lg h-full flex flex-col">
    <header v-if="group" class="p-4 border-b">
      <h2 class="text-xl font-semibold text-slate-800 truncate">{{ group }}</h2>
    </header>

    <main class="flex-1 p-4 overflow-y-auto bg-gray-50">
      <div v-if="isLoading" class="flex justify-center items-center h-full">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
      <div v-else-if="messages.length > 0" class="space-y-4">
        <div v-for="message in messages" :key="message.id">
          <div class="flex items-start gap-3">
            <div class="w-10 h-10 rounded-full bg-gray-300 flex-shrink-0"></div>
            <div class="flex-1">
              <div class="flex items-baseline gap-2">
                <span class="font-bold text-slate-800">{{ message.senderName }}</span>
                <span class="text-xs text-gray-500">{{ message.messageTime }}</span>
              </div>

              <div v-if="message.isReply && message.repliedMessageContent" class="mt-1 p-2 bg-gray-100 rounded-lg border-l-4 border-blue-300">
                <p class="font-semibold text-sm text-blue-800">{{ message.repliedToUser }}</p>
                <p class="text-sm text-gray-600 truncate" v-html="message.repliedMessageContent"></p>
              </div>

              <div class="mt-1 text-slate-700" v-html="message.messageContent"></div>

              <div v-if="message.imageUrl" class="mt-2">
                <img :src="message.imageUrl" alt="聊天图片" class="rounded-lg max-w-xs max-h-64 object-cover cursor-pointer" @click="() => window.open(message.imageUrl, '_blank')">
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="flex items-center justify-center h-full text-gray-500">
        <p>{{ group ? '该群组没有消息' : '请从左侧选择一个群组' }}</p>
      </div>
    </main>
  </div>
</template>