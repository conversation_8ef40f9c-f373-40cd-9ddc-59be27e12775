<script setup lang="ts">
import { ref, computed } from 'vue'

interface Props {
  isLoading: boolean
  uploadProgress: number
  uploadError: string | null
}

const props = defineProps<Props>()

const emit = defineEmits<{
  (e: 'files-selected', files: File[]): void
  (e: 'upload'): void
  (e: 'reset'): void
}>()

const fileInput = ref<HTMLInputElement | null>(null)
const dragOver = ref(false)
const selectedFiles = ref<File[]>([])

const acceptedTypes = '.html,.htm'
const maxFileSize = 10 * 1024 * 1024 // 10MB
const maxFileCount = 5 // 最多5个文件
const maxTotalSize = 50 * 1024 * 1024 // 总大小最多50MB

const filesInfo = computed(() => {
  return selectedFiles.value.map(file => ({
    name: file.name,
    size: formatFileSize(file.size),
    type: file.type || 'text/html'
  }))
})

const totalSize = computed(() => {
  return selectedFiles.value.reduce((sum, file) => sum + file.size, 0)
})

const hasFiles = computed(() => selectedFiles.value.length > 0)

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

const validateFile = (file: File): string | null => {
  const validExtensions = ['.html', '.htm']
  const hasValidExtension = validExtensions.some(ext => file.name.toLowerCase().endsWith(ext))
  if (!hasValidExtension) {
    return '请选择导出的聊天记录HTML文件'
  }
  if (file.size > maxFileSize) {
    return `文件大小不能超过 ${formatFileSize(maxFileSize)}`
  }
  return null
}

const handleFileSelect = (file: File) => {
  const error = validateFile(file)
  if (error) {
    alert(error)
    return
  }
  selectedFile.value = file
  emit('file-selected', file)
}

const onFileInputChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files?.[0]) {
    handleFileSelect(target.files[0])
  }
}

const onDrop = (event: DragEvent) => {
  event.preventDefault()
  dragOver.value = false
  if (props.isLoading) return
  if (event.dataTransfer?.files?.[0]) {
    handleFileSelect(event.dataTransfer.files[0])
  }
}

const triggerFileInput = () => {
  if (!props.isLoading) {
    fileInput.value?.click()
  }
}

const handleReset = () => {
  selectedFile.value = null
  if (fileInput.value) {
    fileInput.value.value = ''
  }
  emit('reset')
}
</script>

<template>
  <div class="bg-white rounded-xl shadow-lg p-6 space-y-4">
    <h2 class="text-xl font-semibold text-slate-800">上传聊天记录</h2>

    <div
      v-if="!selectedFile"
      @click="triggerFileInput"
      @dragover.prevent="dragOver = !isLoading"
      @dragleave.prevent="dragOver = false"
      @drop="onDrop"
      :class="[
        'border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors',
        isLoading ? 'bg-gray-50 cursor-not-allowed' :
        dragOver ? 'border-blue-400 bg-blue-50' : 'border-gray-300 hover:border-blue-400'
      ]"
    >
      <input ref="fileInput" type="file" :accept="acceptedTypes" @change="onFileInputChange" :disabled="isLoading" class="hidden">
      <div class="flex flex-col items-center space-y-2">
        <p class="text-gray-600">拖拽文件到此或 <span class="font-medium text-blue-600">点击选择</span></p>
        <p class="text-sm text-gray-500">支持 .html/.htm, 最大 10MB</p>
      </div>
    </div>

    <div v-if="selectedFile && !isLoading" class="bg-gray-50 rounded-lg p-3">
      <div class="flex items-center justify-between">
        <p class="text-sm font-medium text-gray-800 truncate">{{ selectedFile.name }} ({{ fileInfo?.size }})</p>
        <button @click="handleReset" class="text-gray-500 hover:text-gray-700">&times;</button>
      </div>
    </div>

    <div v-if="isLoading" class="w-full bg-gray-200 rounded-full h-2.5">
      <div class="bg-blue-600 h-2.5 rounded-full" :style="{ width: uploadProgress + '%' }"></div>
    </div>

    <div v-if="uploadError" class="text-red-600 text-sm p-2 bg-red-50 rounded-md">
      {{ uploadError }}
    </div>

    <button
      @click="$emit('upload')"
      :disabled="!selectedFile || isLoading"
      class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:bg-gray-300 transition-colors font-medium"
    >
      {{ isLoading ? '上传中...' : '上传并解析' }}
    </button>
  </div>
</template>
