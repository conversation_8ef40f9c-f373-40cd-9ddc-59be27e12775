<script setup lang="ts">
import { ref, computed } from 'vue'

interface Props {
  disabled?: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  filesSelected: [files: File[]]
}>()

const fileInput = ref<HTMLInputElement | null>(null)
const dragOver = ref(false)
const selectedFiles = ref<File[]>([])

const acceptedTypes = '.html,.htm'
const maxFileSize = 10 * 1024 * 1024 // 10MB

const filesInfo = computed(() => {
  return selectedFiles.value.map(file => ({
    name: file.name,
    size: formatFileSize(file.size),
    type: file.type || 'text/html'
  }))
})

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const validateFile = (file: File): string | null => {
  // 检查文件类型
  const validTypes = ['text/html', 'text/htm', 'application/xhtml+xml']
  const validExtensions = ['.html', '.htm']

  const hasValidType = validTypes.includes(file.type) ||
      validExtensions.some(ext => file.name.toLowerCase().endsWith(ext))

  if (!hasValidType) {
    return '请选择HTML文件 (.html 或 .htm)'
  }

  // 检查文件大小
  if (file.size > maxFileSize) {
    return `文件大小不能超过 ${formatFileSize(maxFileSize)}`
  }

  return null
}

const handleFilesSelect = (files: FileList | File[]) => {
  const newFiles: File[] = []
  const errors: string[] = []

  for (const file of Array.from(files)) {
    const error = validateFile(file)
    if (error) {
      errors.push(`${file.name}: ${error}`)
    } else {
      newFiles.push(file)
    }
  }

  if (errors.length > 0) {
    alert(errors.join('\n'))
  }

  selectedFiles.value = [...selectedFiles.value, ...newFiles]
  emit('filesSelected', selectedFiles.value)
}

const onFileInputChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files) {
    handleFilesSelect(target.files)
  }
}

const onDrop = (event: DragEvent) => {
  event.preventDefault()
  dragOver.value = false

  if (props.disabled) return

  const files = event.dataTransfer?.files
  if (files) {
    handleFilesSelect(files)
  }
}

const onDragOver = (event: DragEvent) => {
  event.preventDefault()
  if (!props.disabled) {
    dragOver.value = true
  }
}

const onDragLeave = () => {
  dragOver.value = false
}

const triggerFileInput = () => {
  if (!props.disabled) {
    fileInput.value?.click()
  }
}

const clearFile = (index: number) => {
  selectedFiles.value.splice(index, 1)
  emit('filesSelected', selectedFiles.value)
  // If all files are cleared, reset the file input
  if (selectedFiles.value.length === 0) {
    resetFileInput()
  }
}

const resetFileInput = () => {
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}
</script>

<template>
  <div class="space-y-4">
    <!-- 拖拽上传区域 -->
    <div
        @drop="onDrop"
        @dragover="onDragOver"
        @dragleave="onDragLeave"
        @click="triggerFileInput"
        :class="[
        'border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors',
        disabled
          ? 'border-gray-200 bg-gray-50 cursor-not-allowed'
          : dragOver
            ? 'border-blue-400 bg-blue-50'
            : 'border-gray-300 hover:border-blue-400 hover:bg-blue-50'
      ]"
    >
      <input
          ref="fileInput"
          type="file"
          :accept="acceptedTypes"
          @change="onFileInputChange"
          :disabled="disabled"
          multiple
          class="hidden"
      >

      <div class="flex flex-col items-center space-y-3">
        <svg
            :class="[
            'w-12 h-12',
            disabled ? 'text-gray-300' : 'text-gray-400'
          ]"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
        >
          <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
          />
        </svg>

        <div>
          <p :class="disabled ? 'text-gray-400' : 'text-gray-600'">
            <span class="font-medium text-blue-600">点击上传</span>
            或拖拽HTML文件到此区域
          </p>
          <p :class="['text-sm mt-1', disabled ? 'text-gray-300' : 'text-gray-500']">
            支持 .html, .htm 格式，最大 {{ formatFileSize(maxFileSize) }}
          </p>
        </div>
      </div>
    </div>

    <!-- 已选择文件信息 -->
    <div v-if="filesInfo.length > 0" class="space-y-2">
      <div
          v-for="(file, index) in filesInfo"
          :key="file.name + index"
          class="bg-gray-50 rounded-lg p-4"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3 overflow-hidden">
            <svg class="w-8 h-8 text-orange-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd" />
            </svg>

            <div class="truncate">
              <p class="font-medium text-gray-900 truncate">{{ file.name }}</p>
              <p class="text-sm text-gray-500">{{ file.size }} • {{ file.type }}</p>
            </div>
          </div>

          <button
              @click.stop="clearFile(index)"
              :disabled="disabled"
              class="text-gray-400 hover:text-gray-600 disabled:cursor-not-allowed flex-shrink-0 ml-4"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
