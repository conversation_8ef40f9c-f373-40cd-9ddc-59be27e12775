<script setup lang="ts">
interface Props {
  groups: string[]
  selectedGroup: string | null
}

defineProps<Props>()
const emit = defineEmits<{
  (e: 'group-selected', group: string): void
}>()
</script>

<template>
  <div class="bg-white rounded-xl shadow-lg p-6">
    <h2 class="text-xl font-semibold text-slate-800 mb-4">群组列表</h2>
    <ul v-if="groups.length > 0" class="space-y-2">
      <li
        v-for="group in groups"
        :key="group"
        @click="emit('group-selected', group)"
        :class="[
          'p-3 rounded-lg cursor-pointer transition-colors truncate',
          selectedGroup === group ? 'bg-blue-500 text-white' : 'bg-gray-100 hover:bg-gray-200'
        ]"
      >
        {{ group }}
      </li>
    </ul>
    <div v-else class="text-center py-8 text-gray-500">
      <p>没有找到群组。</p>
      <p class="text-sm">请先上传聊天记录文件。</p>
    </div>
  </div>
</template>