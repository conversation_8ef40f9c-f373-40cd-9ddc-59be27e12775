<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  progress: number
}

const props = defineProps<Props>()

const progressText = computed(() => {
  if (props.progress < 30) return '正在上传文件...'
  if (props.progress < 60) return '文件上传完成，开始解析...'
  if (props.progress < 90) return '正在解析HTML结构...'
  return '解析完成，正在生成结果...'
})

const progressClass = computed(() => {
  if (props.progress < 25) return 'bg-red-500'
  if (props.progress < 50) return 'bg-yellow-500'
  if (props.progress < 75) return 'bg-blue-500'
  return 'bg-green-500'
})
</script>

<template>
  <div class="space-y-4">
    <!-- 进度条 -->
    <div class="relative">
      <div class="flex mb-2 items-center justify-between">
        <div>
          <span class="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-blue-600 bg-blue-200">
            进度
          </span>
        </div>
        <div class="text-right">
          <span class="text-xs font-semibold inline-block text-blue-600">
            {{ Math.round(progress) }}%
          </span>
        </div>
      </div>

      <div class="overflow-hidden h-3 mb-4 text-xs flex rounded-full bg-gray-200">
        <div
            :style="{ width: `${progress}%` }"
            :class="[
            'shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center transition-all duration-300 ease-out',
            progressClass
          ]"
        />
      </div>
    </div>

    <!-- 进度状态文本 -->
    <div class="flex items-center space-x-3">
      <div class="flex-shrink-0">
        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
          <div v-if="progress < 100" class="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
          <svg v-else class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
        </div>
      </div>

      <div class="flex-1">
        <p class="text-sm font-medium text-gray-900">{{ progressText }}</p>
        <div class="flex items-center mt-1">
          <div class="flex space-x-1">
            <div
                v-for="i in 4"
                :key="i"
                :class="[
                'w-2 h-2 rounded-full transition-colors duration-200',
                progress >= (i * 25) ? 'bg-blue-600' : 'bg-gray-300'
              ]"
            />
          </div>
          <span class="ml-2 text-xs text-gray-500">
            步骤 {{ Math.min(Math.floor(progress / 25) + 1, 4) }} / 4
          </span>
        </div>
      </div>
    </div>

    <!-- 详细步骤 -->
    <div class="bg-gray-50 rounded-lg p-4">
      <h4 class="text-sm font-medium text-gray-900 mb-3">解析步骤</h4>
      <div class="space-y-2">
        <div
            v-for="(step, index) in [
            '上传HTML文件到服务器',
            '验证文件格式和大小',
            '解析HTML DOM结构',
            '生成结构化数据并返回'
          ]"
            :key="index"
            :class="[
            'flex items-center text-sm',
            progress > (index + 1) * 25 ? 'text-green-600' :
            progress > index * 25 ? 'text-blue-600' : 'text-gray-400'
          ]"
        >
          <div
              :class="[
              'w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center',
              progress > (index + 1) * 25 ? 'border-green-600 bg-green-600' :
              progress > index * 25 ? 'border-blue-600' : 'border-gray-300'
            ]"
          >
            <svg
                v-if="progress > (index + 1) * 25"
                class="w-2 h-2 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
            >
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
            </svg>
            <div
                v-else-if="progress > index * 25 && progress <= (index + 1) * 25"
                class="w-2 h-2 bg-blue-600 rounded-full"
            />
          </div>
          {{ step }}
        </div>
      </div>
    </div>
  </div>
</template>
