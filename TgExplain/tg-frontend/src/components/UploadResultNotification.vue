<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  results: { successful: string[], failed: Record<string, string> } | null
  show: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  (e: 'close'): void
}>()

const notificationType = computed(() => {
  if (!props.results) return 'info'
  
  const successCount = props.results.successful.length
  const failCount = Object.keys(props.results.failed).length
  
  if (successCount > 0 && failCount === 0) return 'success'
  if (successCount > 0 && failCount > 0) return 'warning'
  return 'error'
})

const notificationTitle = computed(() => {
  if (!props.results) return ''
  
  const successCount = props.results.successful.length
  const failCount = Object.keys(props.results.failed).length
  
  if (successCount > 0 && failCount === 0) {
    return `上传成功！`
  } else if (successCount > 0 && failCount > 0) {
    return `部分文件上传成功`
  } else {
    return `上传失败`
  }
})

const notificationMessage = computed(() => {
  if (!props.results) return ''
  
  const successCount = props.results.successful.length
  const failCount = Object.keys(props.results.failed).length
  
  if (successCount > 0 && failCount === 0) {
    return `所有 ${successCount} 个文件已成功上传并处理完成。`
  } else if (successCount > 0 && failCount > 0) {
    return `${successCount} 个文件成功，${failCount} 个文件失败。`
  } else {
    return `所有 ${failCount} 个文件上传失败。`
  }
})

const closeNotification = () => {
  emit('close')
}
</script>

<template>
  <Transition
    enter-active-class="transition ease-out duration-300"
    enter-from-class="opacity-0 transform translate-y-2"
    enter-to-class="opacity-100 transform translate-y-0"
    leave-active-class="transition ease-in duration-200"
    leave-from-class="opacity-100 transform translate-y-0"
    leave-to-class="opacity-0 transform translate-y-2"
  >
    <div
      v-if="show && results"
      class="fixed top-4 right-4 max-w-md w-full bg-white rounded-lg shadow-lg border-l-4 z-50"
      :class="{
        'border-green-500': notificationType === 'success',
        'border-yellow-500': notificationType === 'warning',
        'border-red-500': notificationType === 'error',
        'border-blue-500': notificationType === 'info'
      }"
    >
      <div class="p-4">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <!-- Success Icon -->
            <svg
              v-if="notificationType === 'success'"
              class="w-6 h-6 text-green-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <!-- Warning Icon -->
            <svg
              v-else-if="notificationType === 'warning'"
              class="w-6 h-6 text-yellow-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
            <!-- Error Icon -->
            <svg
              v-else-if="notificationType === 'error'"
              class="w-6 h-6 text-red-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          
          <div class="ml-3 w-0 flex-1">
            <p class="text-sm font-medium text-gray-900">
              {{ notificationTitle }}
            </p>
            <p class="mt-1 text-sm text-gray-500">
              {{ notificationMessage }}
            </p>
            
            <!-- 失败详情 -->
            <div v-if="Object.keys(results.failed).length > 0" class="mt-3">
              <details class="text-sm">
                <summary class="cursor-pointer text-red-600 hover:text-red-800 font-medium">
                  查看失败详情 ({{ Object.keys(results.failed).length }} 个文件)
                </summary>
                <div class="mt-2 max-h-32 overflow-y-auto bg-red-50 rounded p-2">
                  <ul class="space-y-1">
                    <li
                      v-for="[filename, error] in Object.entries(results.failed)"
                      :key="filename"
                      class="text-xs text-red-700"
                    >
                      <span class="font-medium">{{ filename }}:</span>
                      <span class="ml-1">{{ error }}</span>
                    </li>
                  </ul>
                </div>
              </details>
            </div>
            
            <!-- 成功详情 -->
            <div v-if="results.successful.length > 0" class="mt-3">
              <details class="text-sm">
                <summary class="cursor-pointer text-green-600 hover:text-green-800 font-medium">
                  查看成功详情 ({{ results.successful.length }} 个文件)
                </summary>
                <div class="mt-2 max-h-32 overflow-y-auto bg-green-50 rounded p-2">
                  <ul class="space-y-1">
                    <li
                      v-for="success in results.successful"
                      :key="success"
                      class="text-xs text-green-700"
                    >
                      {{ success }}
                    </li>
                  </ul>
                </div>
              </details>
            </div>
          </div>
          
          <div class="ml-4 flex-shrink-0 flex">
            <button
              @click="closeNotification"
              class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <span class="sr-only">关闭</span>
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </Transition>
</template>
