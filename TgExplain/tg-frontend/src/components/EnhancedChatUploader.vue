<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useBatchUpload } from '../composables/useBatchUpload'

interface Props {
  disabled?: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  (e: 'upload-completed', results: { successful: string[], failed: Record<string, string> }): void
  (e: 'reset'): void
}>()

const fileInput = ref<HTMLInputElement | null>(null)
const dragOver = ref(false)
const selectedFiles = ref<File[]>([])
const showLimitWarning = ref(false)
const warningMessage = ref('')

// 配置常量
const acceptedTypes = '.html,.htm'
const maxFileSize = 10 * 1024 * 1024 // 10MB
const recommendedFileCount = 5 // 建议文件数量
const maxTotalSize = 50 * 1024 * 1024 // 总大小最多50MB
const batchSize = 3 // 每批次文件数量

// 使用批次上传管理器
const batchUpload = useBatchUpload({
  maxFilesPerBatch: batchSize,
  maxRetries: 3,
  retryDelay: 2000
})

// 计算属性
const filesInfo = computed(() => {
  return selectedFiles.value.map(file => ({
    name: file.name,
    size: formatFileSize(file.size),
    type: file.type || 'text/html'
  }))
})

const totalSize = computed(() => {
  return selectedFiles.value.reduce((sum, file) => sum + file.size, 0)
})

const hasFiles = computed(() => selectedFiles.value.length > 0)

const willUseBatches = computed(() => {
  return selectedFiles.value.length > batchSize
})

const estimatedBatches = computed(() => {
  return Math.ceil(selectedFiles.value.length / batchSize)
})

const limitInfo = computed(() => {
  const fileCount = selectedFiles.value.length
  const totalSizeValue = totalSize.value
  
  if (fileCount > recommendedFileCount) {
    return {
      type: 'warning',
      message: `您选择了 ${fileCount} 个文件，超过建议的 ${recommendedFileCount} 个文件。系统将自动分 ${estimatedBatches.value} 个批次上传。`
    }
  }
  
  if (totalSizeValue > maxTotalSize) {
    return {
      type: 'error',
      message: `文件总大小 ${formatFileSize(totalSizeValue)} 超过限制 ${formatFileSize(maxTotalSize)}`
    }
  }
  
  return null
})

// 工具函数
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

const validateFile = (file: File): string | null => {
  // 检查文件类型
  const validTypes = ['text/html', 'text/htm', 'application/xhtml+xml']
  const validExtensions = ['.html', '.htm']
  
  const hasValidType = validTypes.includes(file.type) || 
      validExtensions.some(ext => file.name.toLowerCase().endsWith(ext))
  
  if (!hasValidType) {
    return '请选择HTML文件 (.html 或 .htm)'
  }
  
  // 检查文件大小
  if (file.size > maxFileSize) {
    return `文件大小不能超过 ${formatFileSize(maxFileSize)}`
  }
  
  return null
}

const validateFiles = (files: File[]): { validFiles: File[], errors: string[] } => {
  const validFiles: File[] = []
  const errors: string[] = []
  
  // 验证每个文件
  for (const file of files) {
    const error = validateFile(file)
    if (error) {
      errors.push(`${file.name}: ${error}`)
    } else {
      validFiles.push(file)
    }
  }
  
  return { validFiles, errors }
}

// 事件处理
const handleFilesSelect = (files: FileList | File[]) => {
  const fileArray = Array.from(files)
  const { validFiles, errors } = validateFiles(fileArray)
  
  if (errors.length > 0) {
    alert(errors.join('\n'))
  }
  
  if (validFiles.length > 0) {
    selectedFiles.value = validFiles
    
    // 显示限制警告
    const info = limitInfo.value
    if (info) {
      showLimitWarning.value = true
      warningMessage.value = info.message
    } else {
      showLimitWarning.value = false
    }
  }
}

const onFileInputChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files.length > 0) {
    handleFilesSelect(target.files)
  }
}

const onDrop = (event: DragEvent) => {
  event.preventDefault()
  dragOver.value = false
  if (props.disabled || batchUpload.isUploading.value) return
  if (event.dataTransfer?.files && event.dataTransfer.files.length > 0) {
    handleFilesSelect(event.dataTransfer.files)
  }
}

const onDragOver = (event: DragEvent) => {
  event.preventDefault()
  if (!props.disabled && !batchUpload.isUploading.value) {
    dragOver.value = true
  }
}

const onDragLeave = (event: DragEvent) => {
  event.preventDefault()
  dragOver.value = false
}

const triggerFileInput = () => {
  if (!props.disabled && !batchUpload.isUploading.value) {
    fileInput.value?.click()
  }
}

const removeFile = (index: number) => {
  selectedFiles.value.splice(index, 1)
  if (selectedFiles.value.length === 0) {
    handleReset()
  }
}

const handleReset = () => {
  selectedFiles.value = []
  showLimitWarning.value = false
  warningMessage.value = ''
  batchUpload.reset()
  if (fileInput.value) {
    fileInput.value.value = ''
  }
  emit('reset')
}

const startUpload = async () => {
  if (selectedFiles.value.length === 0) return
  
  // 检查总大小限制
  if (totalSize.value > maxTotalSize) {
    alert(`文件总大小超过限制 ${formatFileSize(maxTotalSize)}`)
    return
  }
  
  batchUpload.initializeBatches(selectedFiles.value)
  await batchUpload.startBatchUpload()
  
  // 上传完成后发送结果
  const results = batchUpload.getAllResults()
  emit('upload-completed', results)
}

const pauseUpload = () => {
  batchUpload.pauseUpload()
}

const resumeUpload = () => {
  batchUpload.resumeUpload()
}

const cancelUpload = () => {
  batchUpload.cancelUpload()
}

const retryFailed = () => {
  batchUpload.retryFailedBatches()
}

// 监听上传完成
watch(() => batchUpload.isCompleted.value, (completed) => {
  if (completed) {
    const results = batchUpload.getAllResults()
    emit('upload-completed', results)
  }
})
</script>

<template>
  <div class="bg-white rounded-lg shadow-md p-6">
    <h2 class="text-xl font-semibold mb-4">上传聊天记录</h2>

    <!-- 限制信息提示 -->
    <div class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
      <div class="flex items-start">
        <svg class="w-5 h-5 text-blue-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <div class="text-sm text-blue-700">
          <p class="font-medium mb-1">上传限制说明：</p>
          <ul class="space-y-1 text-blue-600">
            <li>• 支持 .html/.htm 格式文件</li>
            <li>• 单文件最大 {{ formatFileSize(maxFileSize) }}</li>
            <li>• 建议单次上传不超过 {{ recommendedFileCount }} 个文件</li>
            <li>• 总大小不超过 {{ formatFileSize(maxTotalSize) }}</li>
            <li>• 超过 {{ batchSize }} 个文件将自动分批次上传</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 限制警告 -->
    <div v-if="showLimitWarning && limitInfo" class="mb-4 p-3 rounded-lg border" :class="{
      'bg-yellow-50 border-yellow-200': limitInfo.type === 'warning',
      'bg-red-50 border-red-200': limitInfo.type === 'error'
    }">
      <div class="flex items-start">
        <svg class="w-5 h-5 mt-0.5 mr-2 flex-shrink-0" :class="{
          'text-yellow-500': limitInfo.type === 'warning',
          'text-red-500': limitInfo.type === 'error'
        }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
        <p class="text-sm" :class="{
          'text-yellow-700': limitInfo.type === 'warning',
          'text-red-700': limitInfo.type === 'error'
        }">{{ warningMessage }}</p>
      </div>
    </div>

    <!-- 文件选择区域 -->
    <div
      v-if="!hasFiles && !batchUpload.isUploading.value"
      @click="triggerFileInput"
      @dragover="onDragOver"
      @dragleave="onDragLeave"
      @drop="onDrop"
      :class="[
        'border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors',
        disabled || batchUpload.isUploading.value ? 'bg-gray-50 cursor-not-allowed border-gray-200' :
        dragOver ? 'border-blue-400 bg-blue-50' : 'border-gray-300 hover:border-blue-400'
      ]"
    >
      <input
        ref="fileInput"
        type="file"
        :accept="acceptedTypes"
        @change="onFileInputChange"
        :disabled="disabled || batchUpload.isUploading.value"
        multiple
        class="hidden"
      >
      <div class="flex flex-col items-center space-y-3">
        <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
        </svg>
        <div>
          <p class="text-gray-600">
            拖拽文件到此或 <span class="font-medium text-blue-600">点击选择</span>
          </p>
          <p class="text-sm text-gray-500 mt-1">
            支持多文件选择，系统将智能分批处理
          </p>
        </div>
      </div>
    </div>

    <!-- 已选择文件列表 -->
    <div v-if="hasFiles && !batchUpload.isUploading.value" class="space-y-3">
      <div class="flex items-center justify-between">
        <h3 class="text-sm font-medium text-gray-700">
          已选择 {{ selectedFiles.length }} 个文件 ({{ formatFileSize(totalSize) }})
          <span v-if="willUseBatches" class="text-blue-600">
            - 将分 {{ estimatedBatches }} 个批次上传
          </span>
        </h3>
        <button
          @click="handleReset"
          class="text-sm text-gray-500 hover:text-gray-700 underline"
        >
          清空所有
        </button>
      </div>

      <div class="max-h-40 overflow-y-auto space-y-2 border rounded-lg p-3 bg-gray-50">
        <div
          v-for="(fileInfo, index) in filesInfo"
          :key="index"
          class="flex items-center justify-between bg-white rounded-lg p-2 shadow-sm"
        >
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium text-gray-800 truncate">{{ fileInfo.name }}</p>
            <p class="text-xs text-gray-500">{{ fileInfo.size }}</p>
          </div>
          <button
            @click="removeFile(index)"
            class="ml-2 text-gray-400 hover:text-red-500 transition-colors"
            title="移除文件"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- 批次上传进度 -->
    <div v-if="batchUpload.isUploading.value || batchUpload.batches.value.length > 0" class="space-y-4">
      <!-- 整体进度 -->
      <div class="space-y-2">
        <div class="flex justify-between text-sm text-gray-600">
          <span>整体进度</span>
          <span>{{ batchUpload.overallProgress.value }}%</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-3">
          <div
            class="bg-blue-600 h-3 rounded-full transition-all duration-300"
            :style="{ width: batchUpload.overallProgress.value + '%' }"
          ></div>
        </div>
        <div class="flex justify-between text-xs text-gray-500">
          <span>{{ batchUpload.stats.completedFiles }}/{{ batchUpload.stats.totalFiles }} 文件完成</span>
          <span>{{ batchUpload.stats.completedBatches }}/{{ batchUpload.stats.totalBatches }} 批次完成</span>
        </div>
      </div>

      <!-- 当前批次进度 -->
      <div v-if="batchUpload.currentBatch.value" class="p-3 bg-blue-50 rounded-lg">
        <div class="flex justify-between items-center mb-2">
          <span class="text-sm font-medium text-blue-800">
            当前批次 {{ batchUpload.currentBatchIndex.value + 1 }}/{{ batchUpload.batches.value.length }}
          </span>
          <span class="text-sm text-blue-600">{{ batchUpload.currentBatch.value.progress }}%</span>
        </div>
        <div class="w-full bg-blue-200 rounded-full h-2">
          <div
            class="bg-blue-600 h-2 rounded-full transition-all duration-300"
            :style="{ width: batchUpload.currentBatch.value.progress + '%' }"
          ></div>
        </div>
        <p class="text-xs text-blue-600 mt-1">
          正在上传 {{ batchUpload.currentBatch.value.files.length }} 个文件
        </p>
      </div>
    </div>
</template>
