<script setup lang="ts">
import { computed } from 'vue'

interface HtmlNode {
  tagName: string
  attributes: Record<string, string>
  textContent?: string
  children: HtmlNode[]
  nodeType: number
  depth: number
}

interface ParsedData {
  totalElements: number
  totalTextNodes: number
  structure: HtmlNode[]
  parseTime: number
}

interface Props {
  data: ParsedData
}

const props = defineProps<Props>()

const stats = computed(() => [
  {
    label: 'HTML元素',
    value: props.data.totalElements,
    icon: 'M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z',
    color: 'text-blue-600 bg-blue-100'
  },
  {
    label: '文本节点',
    value: props.data.totalTextNodes,
    icon: 'M4 6h16M4 12h16M4 18h7',
    color: 'text-green-600 bg-green-100'
  },
  {
    label: '结构层级',
    value: getMaxDepth(props.data.structure),
    icon: 'M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z',
    color: 'text-purple-600 bg-purple-100'
  },
  {
    label: '解析耗时',
    value: `${props.data.parseTime.toFixed(2)}ms`,
    icon: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z',
    color: 'text-orange-600 bg-orange-100'
  }
])

function getMaxDepth(nodes: HtmlNode[]): number {
  if (!nodes || nodes.length === 0) return 0

  let maxDepth = 0
  for (const node of nodes) {
    const currentDepth = node.depth || 0
    maxDepth = Math.max(maxDepth, currentDepth)

    if (node.children && node.children.length > 0) {
      maxDepth = Math.max(maxDepth, getMaxDepth(node.children))
    }
  }

  return maxDepth + 1
}

const performanceLevel = computed(() => {
  const time = props.data.parseTime
  if (time < 100) return { level: '优秀', color: 'text-green-600', bg: 'bg-green-100' }
  if (time < 500) return { level: '良好', color: 'text-blue-600', bg: 'bg-blue-100' }
  if (time < 1000) return { level: '一般', color: 'text-yellow-600', bg: 'bg-yellow-100' }
  return { level: '较慢', color: 'text-red-600', bg: 'bg-red-100' }
})
</script>

<template>
  <div class="space-y-6">
    <!-- 统计卡片网格 -->
    <div class="grid grid-cols-2 gap-4">
      <div
          v-for="stat in stats"
          :key="stat.label"
          class="bg-gray-50 rounded-lg p-4"
      >
        <div class="flex items-center">
          <div :class="['p-2 rounded-lg', stat.color]">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="stat.icon" />
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-500">{{ stat.label }}</p>
            <p class="text-lg font-semibold text-gray-900">{{ stat.value }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 性能分析 -->
    <div class="border-t pt-4">
      <h4 class="text-sm font-medium text-gray-900 mb-3">性能分析</h4>

      <div class="space-y-3">
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600">解析性能</span>
          <span :class="['text-xs font-medium px-2 py-1 rounded-full', performanceLevel.color, performanceLevel.bg]">
            {{ performanceLevel.level }}
          </span>
        </div>

        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600">文档复杂度</span>
          <span class="text-sm font-medium text-gray-900">
            {{ data.totalElements > 100 ? '复杂' : data.totalElements > 50 ? '中等' : '简单' }}
          </span>
        </div>

        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600">结构深度</span>
          <span class="text-sm font-medium text-gray-900">
            {{ getMaxDepth(data.structure) }} 层
          </span>
        </div>
      </div>
    </div>

    <!-- 快速统计 -->
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4">
      <div class="flex items-center">
        <svg class="w-5 h-5 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
        </svg>
        <span class="text-sm font-medium text-blue-900">解析完成</span>
      </div>
      <p class="text-sm text-blue-700 mt-1">
        成功解析了包含 <strong>{{ data.totalElements }}</strong> 个元素的HTML文档，
        耗时 <strong>{{ data.parseTime.toFixed(2) }}ms</strong>
      </p>
    </div>

    <!-- 导出选项 -->
    <div class="border-t pt-4">
      <h4 class="text-sm font-medium text-gray-900 mb-3">导出选项</h4>
      <div class="flex space-x-2">
        <button class="flex-1 bg-gray-100 text-gray-700 px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-200 transition-colors">
          导出JSON
        </button>
        <button class="flex-1 bg-gray-100 text-gray-700 px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-200 transition-colors">
          导出XML
        </button>
      </div>
    </div>
  </div>
</template>
