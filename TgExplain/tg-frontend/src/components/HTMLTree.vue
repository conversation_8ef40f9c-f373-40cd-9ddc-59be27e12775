<script setup lang="ts">
import { ref } from 'vue'

interface HtmlNode {
  tagName: string
  attributes: Record<string, string>
  textContent?: string
  children: HtmlNode[]
  nodeType: number
  depth: number
}

interface Props {
  nodes: HtmlNode[]
}

const props = defineProps<Props>()

const expandedNodes = ref<Set<string>>(new Set())
const selectedNode = ref<string | null>(null)

const getNodeId = (node: HtmlNode, index: number, parentId = ''): string => {
  return `${parentId}-${node.tagName}-${index}`
}

const isExpanded = (nodeId: string): boolean => {
  return expandedNodes.value.has(nodeId)
}

const toggleExpanded = (nodeId: string) => {
  if (expandedNodes.value.has(nodeId)) {
    expandedNodes.value.delete(nodeId)
  } else {
    expandedNodes.value.add(nodeId)
  }
}

const selectNode = (nodeId: string) => {
  selectedNode.value = selectedNode.value === nodeId ? null : nodeId
}

const getTagColor = (tagName: string): string => {
  const colors = {
    'html': 'text-red-600',
    'head': 'text-purple-600',
    'body': 'text-blue-600',
    'div': 'text-green-600',
    'span': 'text-orange-600',
    'p': 'text-indigo-600',
    'h1': 'text-pink-600',
    'h2': 'text-pink-600',
    'h3': 'text-pink-600',
    'h4': 'text-pink-600',
    'h5': 'text-pink-600',
    'h6': 'text-pink-600',
    'a': 'text-cyan-600',
    'img': 'text-yellow-600',
    'ul': 'text-teal-600',
    'ol': 'text-teal-600',
    'li': 'text-emerald-600',
    '#text': 'text-gray-600'
  }
  return colors[tagName as keyof typeof colors] || 'text-gray-700'
}

const formatAttributes = (attributes: Record<string, string>): string => {
  return Object.entries(attributes)
      .map(([key, value]) => `${key}="${value}"`)
      .join(' ')
}

const hasChildren = (node: HtmlNode): boolean => {
  return node.children && node.children.length > 0
}

const getIndentLevel = (depth: number): string => {
  return `${depth * 20}px`
}

// 默认展开前几层
const initializeExpanded = () => {
  const autoExpand = (nodes: HtmlNode[], parentId = '', currentDepth = 0) => {
    if (currentDepth >= 3) return // 只自动展开前3层

    nodes.forEach((node, index) => {
      const nodeId = getNodeId(node, index, parentId)
      if (hasChildren(node)) {
        expandedNodes.value.add(nodeId)
        autoExpand(node.children, nodeId, currentDepth + 1)
      }
    })
  }

  autoExpand(props.nodes)
}

// 初始化展开状态
initializeExpanded()
</script>

<template>
  <div class="max-h-96 overflow-y-auto border border-gray-200 rounded-lg bg-white">
    <div class="p-4">
      <div v-if="!nodes || nodes.length === 0" class="text-center py-8 text-gray-500">
        <svg class="w-12 h-12 mx-auto mb-3 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <p>暂无解析结果</p>
      </div>

      <div v-else class="space-y-1">
        <template v-for="(node, index) in nodes" :key="getNodeId(node, index)">
          <div>
            <!-- 节点内容 -->
            <div
                :style="{ paddingLeft: getIndentLevel(node.depth) }"
                :class="[
                'flex items-center py-1 px-2 rounded cursor-pointer hover:bg-gray-50 transition-colors',
                selectedNode === getNodeId(node, index) ? 'bg-blue-50 border border-blue-200' : ''
              ]"
                @click="selectNode(getNodeId(node, index))"
            >
              <!-- 展开/折叠图标 -->
              <button
                  v-if="hasChildren(node)"
                  @click.stop="toggleExpanded(getNodeId(node, index))"
                  class="mr-1 p-0.5 hover:bg-gray-200 rounded"
              >
                <svg
                    :class="[
                    'w-3 h-3 transform transition-transform',
                    isExpanded(getNodeId(node, index)) ? 'rotate-90' : ''
                  ]"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                >
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
              </button>
              <div v-else class="w-4" />

              <!-- 节点图标 -->
              <div class="mr-2">
                <svg v-if="node.tagName === '#text'" class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
                </svg>
                <svg v-else class="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                </svg>
              </div>

              <!-- 节点信息 -->
              <div class="flex-1 min-w-0">
                <div class="flex items-center space-x-2">
                  <span :class="['font-mono text-sm font-medium', getTagColor(node.tagName)]">
                    {{ node.tagName }}
                  </span>

                  <span v-if="Object.keys(node.attributes).length > 0" class="text-xs text-gray-500 truncate">
                    {{ formatAttributes(node.attributes) }}
                  </span>

                  <span v-if="hasChildren(node)" class="text-xs text-gray-400 bg-gray-100 px-1.5 py-0.5 rounded">
                    {{ node.children.length }}
                  </span>
                </div>

                <div v-if="node.textContent && node.textContent.trim()" class="text-xs text-gray-600 truncate mt-0.5">
                  "{{ node.textContent.trim() }}"
                </div>
              </div>
            </div>

            <!-- 子节点 -->
            <div v-if="hasChildren(node) && isExpanded(getNodeId(node, index))">
              <template v-for="(child, childIndex) in node.children" :key="getNodeId(child, childIndex, getNodeId(node, index))">
                <div
                    :style="{ paddingLeft: getIndentLevel(child.depth) }"
                    :class="[
                    'flex items-center py-1 px-2 rounded cursor-pointer hover:bg-gray-50 transition-colors',
                    selectedNode === getNodeId(child, childIndex, getNodeId(node, index)) ? 'bg-blue-50 border border-blue-200' : ''
                  ]"
                    @click="selectNode(getNodeId(child, childIndex, getNodeId(node, index)))"
                >
                  <!-- 展开/折叠图标 -->
                  <button
                      v-if="hasChildren(child)"
                      @click.stop="toggleExpanded(getNodeId(child, childIndex, getNodeId(node, index)))"
                      class="mr-1 p-0.5 hover:bg-gray-200 rounded"
                  >
                    <svg
                        :class="[
                        'w-3 h-3 transform transition-transform',
                        isExpanded(getNodeId(child, childIndex, getNodeId(node, index))) ? 'rotate-90' : ''
                      ]"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                    >
                      <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    </svg>
                  </button>
                  <div v-else class="w-4" />

                  <!-- 节点图标 -->
                  <div class="mr-2">
                    <svg v-if="child.tagName === '#text'" class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
                    </svg>
                    <svg v-else class="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                    </svg>
                  </div>

                  <!-- 节点信息 -->
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center space-x-2">
                      <span :class="['font-mono text-sm font-medium', getTagColor(child.tagName)]">
                        {{ child.tagName }}
                      </span>

                      <span v-if="Object.keys(child.attributes).length > 0" class="text-xs text-gray-500 truncate">
                        {{ formatAttributes(child.attributes) }}
                      </span>

                      <span v-if="hasChildren(child)" class="text-xs text-gray-400 bg-gray-100 px-1.5 py-0.5 rounded">
                        {{ child.children.length }}
                      </span>
                    </div>

                    <div v-if="child.textContent && child.textContent.trim()" class="text-xs text-gray-600 truncate mt-0.5">
                      "{{ child.textContent.trim() }}"
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </template>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="border-t bg-gray-50 px-4 py-3">
      <div class="flex justify-between items-center">
        <div class="text-xs text-gray-500">
          点击节点查看详情，点击箭头展开/折叠
        </div>
        <div class="flex space-x-2">
          <button
              @click="expandedNodes.clear()"
              class="text-xs text-gray-600 hover:text-gray-800 px-2 py-1 hover:bg-gray-100 rounded"
          >
            全部折叠
          </button>
          <button
              @click="initializeExpanded"
              class="text-xs text-gray-600 hover:text-gray-800 px-2 py-1 hover:bg-gray-100 rounded"
          >
            展开前3层
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
