import { ref, computed, reactive } from 'vue'
import { ChatApi, type UploadResult } from '../api/chat'

export interface BatchInfo {
  id: number
  files: File[]
  status: 'waiting' | 'uploading' | 'completed' | 'failed' | 'cancelled'
  progress: number
  result?: UploadResult
  error?: string
  retryCount: number
}

export interface BatchUploadConfig {
  maxFilesPerBatch: number
  maxRetries: number
  retryDelay: number
}

export function useBatchUpload(config: BatchUploadConfig = {
  maxFilesPerBatch: 3,
  maxRetries: 3,
  retryDelay: 2000
}) {
  const batches = ref<BatchInfo[]>([])
  const currentBatchIndex = ref(-1)
  const isUploading = ref(false)
  const isPaused = ref(false)
  const isCancelled = ref(false)
  
  // 统计信息
  const stats = reactive({
    totalFiles: 0,
    completedFiles: 0,
    failedFiles: 0,
    totalBatches: 0,
    completedBatches: 0,
    failedBatches: 0
  })

  // 计算属性
  const overallProgress = computed(() => {
    if (stats.totalBatches === 0) return 0
    const batchProgress = batches.value.reduce((sum, batch) => sum + batch.progress, 0)
    return Math.round(batchProgress / stats.totalBatches)
  })

  const currentBatch = computed(() => {
    return currentBatchIndex.value >= 0 ? batches.value[currentBatchIndex.value] : null
  })

  const canRetry = computed(() => {
    return batches.value.some(batch => batch.status === 'failed' && batch.retryCount < config.maxRetries)
  })

  const hasFailedBatches = computed(() => {
    return batches.value.some(batch => batch.status === 'failed')
  })

  const isCompleted = computed(() => {
    return stats.completedBatches + stats.failedBatches === stats.totalBatches && !isUploading.value
  })

  // 创建批次
  function createBatches(files: File[]): BatchInfo[] {
    const newBatches: BatchInfo[] = []
    
    for (let i = 0; i < files.length; i += config.maxFilesPerBatch) {
      const batchFiles = files.slice(i, i + config.maxFilesPerBatch)
      newBatches.push({
        id: Date.now() + i,
        files: batchFiles,
        status: 'waiting',
        progress: 0,
        retryCount: 0
      })
    }
    
    return newBatches
  }

  // 初始化批次上传
  function initializeBatches(files: File[]) {
    batches.value = createBatches(files)
    currentBatchIndex.value = -1
    isUploading.value = false
    isPaused.value = false
    isCancelled.value = false
    
    // 更新统计信息
    stats.totalFiles = files.length
    stats.completedFiles = 0
    stats.failedFiles = 0
    stats.totalBatches = batches.value.length
    stats.completedBatches = 0
    stats.failedBatches = 0
  }

  // 上传单个批次
  async function uploadBatch(batch: BatchInfo): Promise<void> {
    if (isCancelled.value) return
    
    batch.status = 'uploading'
    batch.progress = 0
    
    try {
      const result = await ChatApi.uploadFiles(batch.files, (progress) => {
        if (!isCancelled.value) {
          batch.progress = progress
        }
      })
      
      if (isCancelled.value) {
        batch.status = 'cancelled'
        return
      }
      
      batch.result = result
      batch.status = 'completed'
      batch.progress = 100
      
      // 更新统计
      stats.completedBatches++
      stats.completedFiles += batch.files.length
      
    } catch (error) {
      if (isCancelled.value) {
        batch.status = 'cancelled'
        return
      }
      
      batch.error = error instanceof Error ? error.message : '上传失败'
      batch.status = 'failed'
      batch.retryCount++
      
      // 更新统计
      stats.failedBatches++
      stats.failedFiles += batch.files.length
    }
  }

  // 开始批次上传
  async function startBatchUpload(): Promise<void> {
    if (isUploading.value || batches.value.length === 0) return
    
    isUploading.value = true
    isPaused.value = false
    isCancelled.value = false
    
    for (let i = 0; i < batches.value.length; i++) {
      if (isCancelled.value) break
      
      // 检查是否暂停
      while (isPaused.value && !isCancelled.value) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
      
      if (isCancelled.value) break
      
      const batch = batches.value[i]
      if (batch.status === 'waiting' || batch.status === 'failed') {
        currentBatchIndex.value = i
        await uploadBatch(batch)
        
        // 如果失败且还有重试次数，延迟后重试
        if (batch.status === 'failed' && batch.retryCount < config.maxRetries) {
          await new Promise(resolve => setTimeout(resolve, config.retryDelay))
          if (!isCancelled.value && !isPaused.value) {
            await uploadBatch(batch)
          }
        }
      }
    }
    
    isUploading.value = false
    currentBatchIndex.value = -1
  }

  // 暂停上传
  function pauseUpload() {
    isPaused.value = true
  }

  // 继续上传
  function resumeUpload() {
    isPaused.value = false
  }

  // 取消上传
  function cancelUpload() {
    isCancelled.value = true
    isPaused.value = false
    isUploading.value = false
    currentBatchIndex.value = -1
    
    // 将所有等待和上传中的批次标记为取消
    batches.value.forEach(batch => {
      if (batch.status === 'waiting' || batch.status === 'uploading') {
        batch.status = 'cancelled'
      }
    })
  }

  // 重试失败的批次
  async function retryFailedBatches() {
    const failedBatches = batches.value.filter(
      batch => batch.status === 'failed' && batch.retryCount < config.maxRetries
    )
    
    if (failedBatches.length === 0) return
    
    // 重置失败批次状态
    failedBatches.forEach(batch => {
      batch.status = 'waiting'
      batch.progress = 0
      batch.error = undefined
      // 更新统计（从失败中移除）
      stats.failedBatches--
      stats.failedFiles -= batch.files.length
    })
    
    await startBatchUpload()
  }

  // 重置所有状态
  function reset() {
    batches.value = []
    currentBatchIndex.value = -1
    isUploading.value = false
    isPaused.value = false
    isCancelled.value = false
    
    Object.assign(stats, {
      totalFiles: 0,
      completedFiles: 0,
      failedFiles: 0,
      totalBatches: 0,
      completedBatches: 0,
      failedBatches: 0
    })
  }

  // 获取所有结果
  function getAllResults(): { successful: string[], failed: Record<string, string> } {
    const successful: string[] = []
    const failed: Record<string, string> = {}
    
    batches.value.forEach(batch => {
      if (batch.result) {
        successful.push(...batch.result.successfulFiles)
        Object.assign(failed, batch.result.failedFiles)
      } else if (batch.error) {
        batch.files.forEach(file => {
          failed[file.name] = batch.error || '上传失败'
        })
      }
    })
    
    return { successful, failed }
  }

  return {
    // 状态
    batches,
    currentBatch,
    currentBatchIndex,
    isUploading,
    isPaused,
    isCancelled,
    stats,
    
    // 计算属性
    overallProgress,
    canRetry,
    hasFailedBatches,
    isCompleted,
    
    // 方法
    initializeBatches,
    startBatchUpload,
    pauseUpload,
    resumeUpload,
    cancelUpload,
    retryFailedBatches,
    reset,
    getAllResults
  }
}
