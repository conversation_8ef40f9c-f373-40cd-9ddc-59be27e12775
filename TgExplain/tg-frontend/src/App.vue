<script setup lang="ts">
import { ref, onMounted } from 'vue'
import ChatUploader from './components/ChatUploader.vue'
import ChatList from './components/ChatList.vue'
import MessageViewer from './components/MessageViewer.vue'
import { ChatA<PERSON>, type ChatMessageDto } from './api/chat'

const isLoading = ref(false)
const uploadProgress = ref(0)
const currentFile = ref<File | null>(null)
const groups = ref<string[]>([])
const selectedGroup = ref<string | null>(null)
const messages = ref<ChatMessageDto[]>([])
const uploadError = ref<string | null>(null)

const loadGroups = async () => {
  try {
    groups.value = await ChatApi.getGroups()
  } catch (error) {
    console.error('Failed to load groups:', error)
  }
}

const handleFileSelect = (file: File) => {
  currentFile.value = file
  uploadError.value = null
}

const handleUpload = async () => {
  if (!currentFile.value) return

  isLoading.value = true
  uploadProgress.value = 0
  uploadError.value = null

  try {
    const result = await ChatApi.uploadFile(currentFile.value, (percent) => {
      uploadProgress.value = percent
    })

    await loadGroups()
    currentFile.value = null
    isLoading.value = false

  } catch (error) {
    isLoading.value = false
    if (error instanceof Error) {
      uploadError.value = error.message
    } else {
      uploadError.value = '上传过程中发生未知错误'
    }
  }
}

const handleGroupSelect = async (groupName: string) => {
  selectedGroup.value = groupName
  try {
    messages.value = await ChatApi.getMessages(groupName)
  } catch (error) {
    console.error('Failed to load messages:', error)
  }
}

const handleReset = () => {
  currentFile.value = null
  uploadError.value = null
  uploadProgress.value = 0
  isLoading.value = false
}

onMounted(() => {
  loadGroups()
})
</script>

<template>
  <div class="min-h-screen bg-gray-100 font-sans">
    <div class="container mx-auto p-4">
      <header class="text-center mb-6">
        <h1 class="text-3xl font-bold text-gray-800">聊天记录查看器</h1>
        <p class="text-gray-600">上传并浏览您的聊天记录</p>
      </header>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Left Column: Upload and Chat List -->
        <div class="md:col-span-1 space-y-6">
          <ChatUploader
            :is-loading="isLoading"
            :upload-progress="uploadProgress"
            :upload-error="uploadError"
            @file-selected="handleFileSelect"
            @upload="handleUpload"
            @reset="handleReset"
          />
          <ChatList
            :groups="groups"
            :selected-group="selectedGroup"
            @group-selected="handleGroupSelect"
          />
        </div>

        <!-- Right Column: Message Viewer -->
        <div class="md:col-span-2">
          <MessageViewer
            :group="selectedGroup"
            :messages="messages"
            :is-loading="false"
          />
        </div>
      </div>
    </div>
  </div>
</template>