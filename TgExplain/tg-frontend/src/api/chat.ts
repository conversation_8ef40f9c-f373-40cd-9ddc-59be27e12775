export interface ChatMessageDto {
    id: number;
    businessId: string;
    groupName: string;
    senderName: string;
    messageContent: string;
    messageTime: string;
    isReply: boolean;
    repliedMessageContent: string;
    repliedToUser: string;
    imageUrl: string;
    htmlFilePath: string;
}

const API_BASE_URL = '/api';

export interface UploadResult {
    message: string;
    successfulFiles: string[];
    failedFiles: Record<string, string>;
}

export class ChatApi {

    /**
     * 上传单个文件（保持向后兼容）
     */
    static async uploadFile(file: File, onProgress: (percent: number) => void): Promise<string> {
        const result = await this.uploadFiles([file], onProgress);
        return result.message;
    }

    /**
     * 上传多个文件
     */
    static async uploadFiles(files: File[], onProgress: (percent: number) => void): Promise<UploadResult> {
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            xhr.open('POST', `${API_BASE_URL}/upload`, true);

            xhr.upload.onprogress = (event) => {
                if (event.lengthComputable) {
                    const percentComplete = Math.round((event.loaded / event.total) * 100);
                    onProgress(percentComplete);
                }
            };

            xhr.onload = () => {
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (xhr.status >= 200 && xhr.status < 400) {
                        resolve(response);
                    } else {
                        reject(new Error(response.error || response.message || '上传失败'));
                    }
                } catch (e) {
                    if (xhr.status >= 200 && xhr.status < 300) {
                        // 处理纯文本响应（向后兼容）
                        resolve({
                            message: xhr.responseText,
                            successfulFiles: [xhr.responseText],
                            failedFiles: {}
                        });
                    } else {
                        reject(new Error(xhr.responseText || '上传失败'));
                    }
                }
            };

            xhr.onerror = () => {
                reject(new Error('网络错误，上传失败'));
            };

            const formData = new FormData();
            files.forEach(file => {
                formData.append('file', file);
            });
            xhr.send(formData);
        });
    }

    static async getGroups(): Promise<string[]> {
        const response = await fetch(`${API_BASE_URL}/groups`);
        if (!response.ok) {
            throw new Error('获取群组列表失败');
        }
        return response.json();
    }

    static async getMessages(groupName: string): Promise<ChatMessageDto[]> {
        const response = await fetch(`${API_BASE_URL}/messages/${encodeURIComponent(groupName)}`);
        if (!response.ok) {
            throw new Error('获取消息失败');
        }
        return response.json();
    }
}
