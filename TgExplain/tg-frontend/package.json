{"name": "vue-vite", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0", "build": "vue-tsc -b && vite build --outDir dist", "lint": "tsc --noEmit && eslint --ext .vue,.js,.cjs,.mjs,.ts,.cts,.mts .", "format": "prettier --write \"src/**/*.{vue,js,ts,cjs,mjs,json,css,scss,md}\"", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.10", "tailwindcss": "^4.1.10", "vue": "^3.5.13"}, "devDependencies": {"@eslint/js": "^9.22.0", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-vue": "^10.0.0", "globals": "^16.0.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "typescript": "~5.7.2", "vite": "^6.2.0", "vue-eslint-parser": "^10.0.0", "vue-tsc": "^2.2.4"}}