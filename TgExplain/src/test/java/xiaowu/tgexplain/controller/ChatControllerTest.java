//package xiaowu.tgexplain.controller;
//
//import com.fasterxml.jackson.databind.ObjectMapper;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.DisplayName;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
//import org.springframework.boot.test.mock.mockito.MockBean;
//import org.springframework.http.MediaType;
//import org.springframework.mock.web.MockMultipartFile;
//import org.springframework.test.context.ActiveProfiles;
//import org.springframework.test.web.servlet.MockMvc;
//import xiaowu.tgexplain.entity.ChatEntity;
//import xiaowu.tgexplain.entity.ChatUserEntity;
//import xiaowu.tgexplain.entity.MessageEntity;
//import xiaowu.tgexplain.entity.ReactionEntity;
//import xiaowu.tgexplain.service.DatabaseStorageService;
//import xiaowu.tgexplain.testdata.TestDataBuilder;
//
//import java.io.IOException;
//import java.nio.charset.StandardCharsets;
//import java.util.List;
//
//import static org.hamcrest.Matchers.*;
//import static org.hamcrest.Matchers.containsString;
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.Mockito.*;
//import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
//import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
//
//@WebMvcTest(ChatController.class)
//@ActiveProfiles("test")
//@DisplayName("ChatController Web Layer Tests")
//class ChatControllerTest {
//
//    @Autowired
//    private MockMvc mockMvc;
//
//    @Autowired
//    private ObjectMapper objectMapper;
//
//    @MockBean
//    private DatabaseStorageService databaseStorageService;
//
//    private ChatEntity testChat;
//    private ChatUserEntity testUser;
//    private MessageEntity testMessage;
//    private ReactionEntity testReaction;
//
//    @BeforeEach
//    void setUp() {
//        testChat = TestDataBuilder.chatEntity()
//            .withId(1L)
//            .withGroupName("测试群组")
//            .build();
//
//        testUser = TestDataBuilder.chatUser()
//            .withId(1L)
//            .withName("测试用户")
//            .withUserInitial("测")
//            .withUserPicColorClass("userpic8")
//            .build();
//
//        testMessage = TestDataBuilder.messageEntity()
//            .withId(1L)
//            .withBusinessId("message123")
//            .withMessageType("default")
//            .withText("测试消息")
//            .withTime("15:30")
//            .withTimestamp("30.05.2025 15:30:00 UTC+08:00")
//            .withJoined(false)
//            .withChat(testChat)
//            .withFromUser(testUser)
//            .build();
//
//        testReaction = TestDataBuilder.reactionEntity()
//            .withId(1L)
//            .withEmoji("👍")
//            .withMessage(testMessage)
//            .withFromUser(testUser)
//            .build();
//    }
//
//    @Test
//    @DisplayName("Should upload HTML file successfully")
//    void shouldUploadHtmlFileSuccessfully() throws Exception {
//        // Given
//        String htmlContent = "<html><body>Test Chat Export</body></html>";
//        MockMultipartFile file = new MockMultipartFile(
//            "file",
//            "chat.html",
//            "text/html",
//            htmlContent.getBytes(StandardCharsets.UTF_8)
//        );
//
//        when(databaseStorageService.processHtmlFile(any())).thenReturn("成功导入 5 条消息");
//
//        // When & Then
//        mockMvc.perform(multipart("/api/chat/upload")
//                .file(file))
//            .andExpect(status().isOk())
//            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//            .andExpect(jsonPath("$.success").value(true))
//            .andExpect(jsonPath("$.message").value("成功导入 5 条消息"));
//
//        verify(databaseStorageService, times(1)).processHtmlFile(any());
//    }
//
//    @Test
//    @DisplayName("Should reject empty file upload")
//    void shouldRejectEmptyFileUpload() throws Exception {
//        // Given
//        MockMultipartFile emptyFile = new MockMultipartFile(
//            "file",
//            "empty.html",
//            "text/html",
//            new byte[0]
//        );
//
//        // When & Then
//        mockMvc.perform(multipart("/api/chat/upload")
//                .file(emptyFile))
//            .andExpect(status().isBadRequest())
//            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//            .andExpect(jsonPath("$.success").value(false))
//            .andExpect(jsonPath("$.message").value("请选择要上传的文件"));
//
//        verify(databaseStorageService, never()).processHtmlFile(any());
//    }
//
//    @Test
//    @DisplayName("Should reject non-HTML file upload")
//    void shouldRejectNonHtmlFileUpload() throws Exception {
//        // Given
//        MockMultipartFile txtFile = new MockMultipartFile(
//            "file",
//            "test.txt",
//            "text/plain",
//            "This is not HTML".getBytes(StandardCharsets.UTF_8)
//        );
//
//        // When & Then
//        mockMvc.perform(multipart("/api/chat/upload")
//                .file(txtFile))
//            .andExpect(status().isBadRequest())
//            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//            .andExpect(jsonPath("$.success").value(false))
//            .andExpect(jsonPath("$.message").value("只支持HTML文件格式"));
//
//        verify(databaseStorageService, never()).processHtmlFile(any());
//    }
//
//    @Test
//    @DisplayName("Should handle file processing error")
//    void shouldHandleFileProcessingError() throws Exception {
//        // Given
//        String htmlContent = "<html><body>Test Chat Export</body></html>";
//        MockMultipartFile file = new MockMultipartFile(
//            "file",
//            "chat.html",
//            "text/html",
//            htmlContent.getBytes(StandardCharsets.UTF_8)
//        );
//
//        when(databaseStorageService.processHtmlFile(any()))
//            .thenThrow(new IOException("文件处理失败"));
//
//        // When & Then
//        mockMvc.perform(multipart("/api/chat/upload")
//                .file(file))
//            .andExpect(status().isInternalServerError())
//            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//            .andExpect(jsonPath("$.success").value(false))
//            .andExpect(jsonPath("$.message").value(containsString("文件处理失败")));
//
//        verify(databaseStorageService, times(1)).processHtmlFile(any());
//    }
//
//    @Test
//    @DisplayName("Should get all chats successfully")
//    void shouldGetAllChatsSuccessfully() throws Exception {
//        // Given
//        ChatEntity chat1 = TestDataBuilder.chatEntity()
//            .withId(1L)
//            .withGroupName("群组1")
//            .build();
//        ChatEntity chat2 = TestDataBuilder.chatEntity()
//            .withId(2L)
//            .withGroupName("群组2")
//            .build();
//
//        when(databaseStorageService.getAllChats()).thenReturn(List.of(chat1, chat2));
//
//        // When & Then
//        mockMvc.perform(get("/api/chat/chats"))
//            .andExpect(status().isOk())
//            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//            .andExpect(jsonPath("$", hasSize(2)))
//            .andExpect(jsonPath("$[0].id").value(1))
//            .andExpect(jsonPath("$[0].groupName").value("群组1"))
//            .andExpect(jsonPath("$[1].id").value(2))
//            .andExpect(jsonPath("$[1].groupName").value("群组2"));
//
//        verify(databaseStorageService, times(1)).getAllChats();
//    }
//
//    @Test
//    @DisplayName("Should return empty list when no chats exist")
//    void shouldReturnEmptyListWhenNoChatsExist() throws Exception {
//        // Given
//        when(databaseStorageService.getAllChats()).thenReturn(List.of());
//
//        // When & Then
//        mockMvc.perform(get("/api/chat/chats"))
//            .andExpect(status().isOk())
//            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//            .andExpect(jsonPath("$", hasSize(0)));
//
//        verify(databaseStorageService, times(1)).getAllChats();
//    }
//
//    @Test
//    @DisplayName("Should get chat messages successfully")
//    void shouldGetChatMessagesSuccessfully() throws Exception {
//        // Given
//        String groupName = "测试群组";
//        MessageEntity message1 = TestDataBuilder.messageEntity()
//            .withId(1L)
//            .withBusinessId("msg1")
//            .withMessageType("default")
//            .withText("消息1")
//            .withTime("15:30")
//            .withFromUser(testUser)
//            .withChat(testChat)
//            .build();
//
//        MessageEntity message2 = TestDataBuilder.messageEntity()
//            .withId(2L)
//            .withBusinessId("msg2")
//            .withMessageType("default")
//            .withText("消息2")
//            .withTime("15:31")
//            .withJoined(true)
//            .withFromUser(testUser)
//            .withChat(testChat)
//            .build();
//
//        when(databaseStorageService.getChatMessages(groupName))
//            .thenReturn(List.of(message1, message2));
//
//        // When & Then
//        mockMvc.perform(get("/api/chat/messages/{groupName}", groupName))
//            .andExpect(status().isOk())
//            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//            .andExpect(jsonPath("$", hasSize(2)))
//            .andExpect(jsonPath("$[0].id").value(1))
//            .andExpect(jsonPath("$[0].businessId").value("msg1"))
//            .andExpect(jsonPath("$[0].messageType").value("default"))
//            .andExpect(jsonPath("$[0].text").value("消息1"))
//            .andExpect(jsonPath("$[0].time").value("15:30"))
//            .andExpect(jsonPath("$[0].joined").value(false))
//            .andExpect(jsonPath("$[0].fromUserName").value("测试用户"))
//            .andExpect(jsonPath("$[0].userInitial").value("测"))
//            .andExpect(jsonPath("$[0].userPicColorClass").value("userpic8"))
//            .andExpect(jsonPath("$[0].chatGroupName").value("测试群组"))
//            .andExpect(jsonPath("$[1].id").value(2))
//            .andExpect(jsonPath("$[1].businessId").value("msg2"))
//            .andExpect(jsonPath("$[1].joined").value(true));
//
//        verify(databaseStorageService, times(1)).getChatMessages(groupName);
//    }
//
//    @Test
//    @DisplayName("Should handle URL encoding in group names")
//    void shouldHandleUrlEncodingInGroupNames() throws Exception {
//        // Given
//        String groupName = "群组 with spaces & symbols";
//        when(databaseStorageService.getChatMessages(groupName)).thenReturn(List.of());
//
//        // When & Then
//        mockMvc.perform(get("/api/chat/messages/{groupName}", groupName))
//            .andExpect(status().isOk())
//            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//            .andExpect(jsonPath("$", hasSize(0)));
//
//        verify(databaseStorageService, times(1)).getChatMessages(groupName);
//    }
//
//    @Test
//    @DisplayName("Should return messages with reactions")
//    void shouldReturnMessagesWithReactions() throws Exception {
//        // Given
//        String groupName = "测试群组";
//
//        MessageEntity messageWithReactions = TestDataBuilder.messageEntity()
//            .withId(1L)
//            .withBusinessId("msg_with_reactions")
//            .withMessageType("default")
//            .withText("有反应的消息")
//            .withFromUser(testUser)
//            .withChat(testChat)
//            .build();
//
//        ReactionEntity reaction1 = TestDataBuilder.reactionEntity()
//            .withId(1L)
//            .withEmoji("👍")
//            .withFromUser(testUser)
//            .withMessage(messageWithReactions)
//            .build();
//
//        ReactionEntity reaction2 = TestDataBuilder.reactionEntity()
//            .withId(2L)
//            .withEmoji("❤️")
//            .withFromUser(testUser)
//            .withMessage(messageWithReactions)
//            .build();
//
//        messageWithReactions.setReactions(List.of(reaction1, reaction2));
//
//        when(databaseStorageService.getChatMessages(groupName))
//            .thenReturn(List.of(messageWithReactions));
//
//        // When & Then
//        mockMvc.perform(get("/api/chat/messages/{groupName}", groupName))
//            .andExpect(status().isOk())
//            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//            .andExpect(jsonPath("$", hasSize(1)))
//            .andExpect(jsonPath("$[0].reactions", hasSize(2)))
//            .andExpect(jsonPath("$[0].reactions[0].emoji").value("👍"))
//            .andExpect(jsonPath("$[0].reactions[0].userName").value("测试用户"))
//            .andExpect(jsonPath("$[0].reactions[1].emoji").value("❤️"))
//            .andExpect(jsonPath("$[0].reactions[1].userName").value("测试用户"));
//    }
//
//    @Test
//    @DisplayName("Should return messages with all optional fields")
//    void shouldReturnMessagesWithAllOptionalFields() throws Exception {
//        // Given
//        String groupName = "测试群组";
//
//        MessageEntity complexMessage = TestDataBuilder.messageEntity()
//            .withId(1L)
//            .withBusinessId("complex_msg")
//            .withMessageType("default")
//            .withText("复杂消息")
//            .withTime("16:45")
//            .withTimestamp("30.05.2025 16:45:30 UTC+08:00")
//            .withJoined(true)
//            .withReplyToMessageBusinessId("reply_to_123")
//            .withImageUrl("photos/image.jpg")
//            .withThumbnailUrl("photos/thumb.jpg")
//            .withPinnedMessageBusinessId("pinned_456")
//            .withPinnedMessageText("置顶消息")
//            .withForwardedMessageJson("{\"text\":\"转发内容\"}")
//            .withDateDetails("日期详情")
//            .withFromUser(testUser)
//            .withChat(testChat)
//            .build();
//
//        when(databaseStorageService.getChatMessages(groupName))
//            .thenReturn(List.of(complexMessage));
//
//        // When & Then
//        mockMvc.perform(get("/api/chat/messages/{groupName}", groupName))
//            .andExpect(status().isOk())
//            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//            .andExpect(jsonPath("$", hasSize(1)))
//            .andExpect(jsonPath("$[0].businessId").value("complex_msg"))
//            .andExpect(jsonPath("$[0].text").value("复杂消息"))
//            .andExpect(jsonPath("$[0].time").value("16:45"))
//            .andExpect(jsonPath("$[0].timestamp").value("30.05.2025 16:45:30 UTC+08:00"))
//            .andExpect(jsonPath("$[0].joined").value(true))
//            .andExpect(jsonPath("$[0].replyToMessageBusinessId").value("reply_to_123"))
//            .andExpect(jsonPath("$[0].imageUrl").value("photos/image.jpg"))
//            .andExpect(jsonPath("$[0].thumbnailUrl").value("photos/thumb.jpg"))
//            .andExpect(jsonPath("$[0].pinnedMessageBusinessId").value("pinned_456"))
//            .andExpect(jsonPath("$[0].pinnedMessageText").value("置顶消息"))
//            .andExpect(jsonPath("$[0].forwardedMessageJson").value("{\"text\":\"转发内容\"}"))
//            .andExpect(jsonPath("$[0].dateDetails").value("日期详情"));
//    }
//
//    @Test
//    @DisplayName("Should handle messages without user")
//    void shouldHandleMessagesWithoutUser() throws Exception {
//        // Given
//        String groupName = "测试群组";
//
//        MessageEntity serviceMessage = TestDataBuilder.messageEntity()
//            .withId(1L)
//            .withBusinessId("service_msg")
//            .withMessageType("service")
//            .withDateDetails("30 May 2025")
//            .withFromUser(null) // Service messages don't have users
//            .withChat(testChat)
//            .build();
//
//        when(databaseStorageService.getChatMessages(groupName))
//            .thenReturn(List.of(serviceMessage));
//
//        // When & Then
//        mockMvc.perform(get("/api/chat/messages/{groupName}", groupName))
//            .andExpect(status().isOk())
//            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//            .andExpect(jsonPath("$", hasSize(1)))
//            .andExpect(jsonPath("$[0].messageType").value("service"))
//            .andExpect(jsonPath("$[0].dateDetails").value("30 May 2025"))
//            .andExpect(jsonPath("$[0].fromUserName").isEmpty())
//            .andExpect(jsonPath("$[0].userInitial").isEmpty())
//            .andExpect(jsonPath("$[0].userPicColorClass").isEmpty());
//    }
//
//    @Test
//    @DisplayName("Should respond to health check")
//    void shouldRespondToHealthCheck() throws Exception {
//        // When & Then
//        mockMvc.perform(get("/api/chat/health"))
//            .andExpect(status().isOk())
//            .andExpect(content().contentType("text/plain;charset=UTF-8"))
//            .andExpect(content().string("TgExplain API is running"));
//    }
//
//    @Test
//    @DisplayName("Should handle CORS preflight requests")
//    void shouldHandleCorsPrefightRequests() throws Exception {
//        // When & Then
//        mockMvc.perform(options("/api/chat/health")
//                .header("Origin", "http://localhost:3000")
//                .header("Access-Control-Request-Method", "GET"))
//            .andExpect(status().isOk());
//    }
//
//    @Test
//    @DisplayName("Should validate file parameter name")
//    void shouldValidateFileParameterName() throws Exception {
//        // Given
//        String htmlContent = "<html><body>Test</body></html>";
//        MockMultipartFile file = new MockMultipartFile(
//            "wrongParameterName", // Wrong parameter name
//            "chat.html",
//            "text/html",
//            htmlContent.getBytes(StandardCharsets.UTF_8)
//        );
//
//        // When & Then
//        mockMvc.perform(multipart("/api/chat/upload")
//                .file(file))
//            .andExpect(status().isBadRequest());
//
//        verify(databaseStorageService, never()).processHtmlFile(any());
//    }
//
//    @Test
//    @DisplayName("Should handle null filename gracefully")
//    void shouldHandleNullFilenameGracefully() throws Exception {
//        // Given
//        MockMultipartFile fileWithNullName = new MockMultipartFile(
//            "file",
//            null, // Null filename
//            "text/html",
//            "content".getBytes(StandardCharsets.UTF_8)
//        );
//
//        // When & Then
//        mockMvc.perform(multipart("/api/chat/upload")
//                .file(fileWithNullName))
//            .andExpect(status().isBadRequest())
//            .andExpect(jsonPath("$.success").value(false))
//            .andExpect(jsonPath("$.message").value("只支持HTML文件格式"));
//
//        verify(databaseStorageService, never()).processHtmlFile(any());
//    }
//}