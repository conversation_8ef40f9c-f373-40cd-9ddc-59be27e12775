//package xiaowu.tgexplain.testdata;
//
//import xiaowu.tgexplain.entity.ChatEntity;
//import xiaowu.tgexplain.entity.ChatUserEntity;
//import xiaowu.tgexplain.entity.MessageEntity;
//import xiaowu.tgexplain.entity.ReactionEntity;
//import xiaowu.tgexplain.model.ChatMessage;
//import xiaowu.tgexplain.model.Message;
//import xiaowu.tgexplain.model.Reaction;
//
//import java.util.ArrayList;
//import java.util.List;
//
//public class TestDataBuilder {
//
//    public static class ChatEntityBuilder {
//        private ChatEntity chat = new ChatEntity();
//
//        public ChatEntityBuilder withId(Long id) {
//            chat.setId(id);
//            return this;
//        }
//
//        public ChatEntityBuilder withGroupName(String groupName) {
//            chat.setGroupName(groupName);
//            return this;
//        }
//
//        public ChatEntity build() {
//            return chat;
//        }
//    }
//
//    public static class ChatUserEntityBuilder {
//        private ChatUserEntity user = new ChatUserEntity();
//
//        public ChatUserEntityBuilder withId(Long id) {
//            user.setId(id);
//            return this;
//        }
//
//        public ChatUserEntityBuilder withName(String name) {
//            user.setName(name);
//            return this;
//        }
//
//        public ChatUserEntityBuilder withUserInitial(String initial) {
//            user.setUserInitial(initial);
//            return this;
//        }
//
//        public ChatUserEntityBuilder withUserPicColorClass(String colorClass) {
//            user.setUserPicColorClass(colorClass);
//            return this;
//        }
//
//        public ChatUserEntity build() {
//            return user;
//        }
//    }
//
//    public static class MessageEntityBuilder {
//        private MessageEntity message = new MessageEntity();
//
//        public MessageEntityBuilder withId(Long id) {
//            message.setId(id);
//            return this;
//        }
//
//        public MessageEntityBuilder withBusinessId(String businessId) {
//            message.setBusinessId(businessId);
//            return this;
//        }
//
//        public MessageEntityBuilder withMessageType(String messageType) {
//            message.setMessageType(messageType);
//            return this;
//        }
//
//        public MessageEntityBuilder withText(String text) {
//            message.setText(text);
//            return this;
//        }
//
//        public MessageEntityBuilder withTime(String time) {
//            message.setTime(time);
//            return this;
//        }
//
//        public MessageEntityBuilder withTimestamp(String timestamp) {
//            message.setTimestamp(timestamp);
//            return this;
//        }
//
//        public MessageEntityBuilder withJoined(boolean isJoined) {
//            message.setJoined(isJoined);
//            return this;
//        }
//
//        public MessageEntityBuilder withFromUser(ChatUserEntity fromUser) {
//            message.setFromUser(fromUser);
//            return this;
//        }
//
//        public MessageEntityBuilder withChat(ChatEntity chat) {
//            message.setChat(chat);
//            return this;
//        }
//
//        public MessageEntityBuilder withReplyToMessageBusinessId(String replyToId) {
//            message.setReplyToMessageBusinessId(replyToId);
//            return this;
//        }
//
//        public MessageEntityBuilder withImageUrl(String imageUrl) {
//            message.setImageUrl(imageUrl);
//            return this;
//        }
//
//        public MessageEntityBuilder withThumbnailUrl(String thumbnailUrl) {
//            message.setThumbnailUrl(thumbnailUrl);
//            return this;
//        }
//
//        public MessageEntityBuilder withForwardedMessageJson(String json) {
//            message.setForwardedMessageJson(json);
//            return this;
//        }
//
//        public MessageEntityBuilder withDateDetails(String dateDetails) {
//            message.setDateDetails(dateDetails);
//            return this;
//        }
//
//        public MessageEntityBuilder withPinnedMessageBusinessId(String pinnedId) {
//            message.setPinnedMessageBusinessId(pinnedId);
//            return this;
//        }
//
//        public MessageEntityBuilder withPinnedMessageText(String pinnedText) {
//            message.setPinnedMessageText(pinnedText);
//            return this;
//        }
//
//        public MessageEntity build() {
//            return message;
//        }
//    }
//
//    public static class ReactionEntityBuilder {
//        private ReactionEntity reaction = new ReactionEntity();
//
//        public ReactionEntityBuilder withId(Long id) {
//            reaction.setId(id);
//            return this;
//        }
//
//        public ReactionEntityBuilder withEmoji(String emoji) {
//            reaction.setEmoji(emoji);
//            return this;
//        }
//
//        public ReactionEntityBuilder withMessage(MessageEntity message) {
//            reaction.setMessage(message);
//            return this;
//        }
//
//        public ReactionEntityBuilder withFromUser(ChatUserEntity fromUser) {
//            reaction.setFromUser(fromUser);
//            return this;
//        }
//
//        public ReactionEntity build() {
//            return reaction;
//        }
//    }
//
//    // DTO Builders
//
//    public static class ChatMessageBuilder {
//        private ChatMessage chatMessage = new ChatMessage();
//
//        public ChatMessageBuilder withGroupName(String groupName) {
//            chatMessage.setGroupName(groupName);
//            return this;
//        }
//
//        public ChatMessageBuilder withMessages(List<Message> messages) {
//            chatMessage.setMessages(messages);
//            return this;
//        }
//
//        public ChatMessageBuilder addMessage(Message message) {
//            if (chatMessage.getMessages() == null) {
//                chatMessage.setMessages(new ArrayList<>());
//            }
//            chatMessage.getMessages().add(message);
//            return this;
//        }
//
//        public ChatMessage build() {
//            return chatMessage;
//        }
//    }
//
//    public static class MessageBuilder {
//        private Message message = new Message();
//
//        public MessageBuilder withId(String id) {
//            message.setId(id);
//            return this;
//        }
//
//        public MessageBuilder withType(String type) {
//            message.setType(type);
//            return this;
//        }
//
//        public MessageBuilder withText(String text) {
//            message.setText(text);
//            return this;
//        }
//
//        public MessageBuilder withFromName(String fromName) {
//            message.setFromName(fromName);
//            return this;
//        }
//
//        public MessageBuilder withTime(String time) {
//            message.setTime(time);
//            return this;
//        }
//
//        public MessageBuilder withTimestamp(String timestamp) {
//            message.setTimestamp(timestamp);
//            return this;
//        }
//
//        public MessageBuilder withJoined(boolean isJoined) {
//            message.setJoined(isJoined);
//            return this;
//        }
//
//        public MessageBuilder withUserInitial(String userInitial) {
//            message.setUserInitial(userInitial);
//            return this;
//        }
//
//        public MessageBuilder withUserPicColorClass(String colorClass) {
//            message.setUserPicColorClass(colorClass);
//            return this;
//        }
//
//        public MessageBuilder withReplyToMessageId(String replyToId) {
//            message.setReplyToMessageId(replyToId);
//            return this;
//        }
//
//        public MessageBuilder withImageUrl(String imageUrl) {
//            message.setImageUrl(imageUrl);
//            return this;
//        }
//
//        public MessageBuilder withThumbnailUrl(String thumbnailUrl) {
//            message.setThumbnailUrl(thumbnailUrl);
//            return this;
//        }
//
//        public MessageBuilder withForwardedMessage(Message forwardedMessage) {
//            message.setForwardedMessage(forwardedMessage);
//            return this;
//        }
//
//        public MessageBuilder withReactions(List<Reaction> reactions) {
//            message.setReactions(reactions);
//            return this;
//        }
//
//        public MessageBuilder withDateDetails(String dateDetails) {
//            message.setDateDetails(dateDetails);
//            return this;
//        }
//
//        public MessageBuilder withPinnedMessageId(String pinnedId) {
//            message.setPinnedMessageId(pinnedId);
//            return this;
//        }
//
//        public MessageBuilder withPinnedMessageText(String pinnedText) {
//            message.setPinnedMessageText(pinnedText);
//            return this;
//        }
//
//        public Message build() {
//            return message;
//        }
//    }
//
//    public static class ReactionBuilder {
//        private Reaction reaction = new Reaction();
//
//        public ReactionBuilder withEmoji(String emoji) {
//            reaction.setEmoji(emoji);
//            return this;
//        }
//
//        public ReactionBuilder withFromName(String fromName) {
//            reaction.setFromName(fromName);
//            return this;
//        }
//
//        public ReactionBuilder withUserInitial(String userInitial) {
//            reaction.setUserInitial(userInitial);
//            return this;
//        }
//
//        public Reaction build() {
//            return reaction;
//        }
//    }
//
//    // Factory methods for quick access
//    public static ChatEntityBuilder chatEntity() {
//        return new ChatEntityBuilder();
//    }
//
//    public static ChatUserEntityBuilder chatUser() {
//        return new ChatUserEntityBuilder();
//    }
//
//    public static MessageEntityBuilder messageEntity() {
//        return new MessageEntityBuilder();
//    }
//
//    public static ReactionEntityBuilder reactionEntity() {
//        return new ReactionEntityBuilder();
//    }
//
//    public static ChatMessageBuilder chatMessage() {
//        return new ChatMessageBuilder();
//    }
//
//    public static MessageBuilder message() {
//        return new MessageBuilder();
//    }
//
//    public static ReactionBuilder reaction() {
//        return new ReactionBuilder();
//    }
//
//    // Sample HTML content for testing
//    public static String createSampleHtmlContent(String groupName, String messageId, String fromName, String text) {
//        return String.format("""
//            <!DOCTYPE html>
//            <html>
//            <head>
//                <meta charset="utf-8"/>
//                <title>Exported Data</title>
//            </head>
//            <body>
//                <div class="page_wrap">
//                    <div class="page_header">
//                        <div class="content block_link">
//                            <div class="text bold">%s</div>
//                        </div>
//                    </div>
//                    <div class="page_body chat_page">
//                        <div class="history">
//                            <div class="message service" id="message-1">
//                                <div class="body details">30 May 2025</div>
//                            </div>
//                            <div class="message default clearfix" id="%s">
//                                <div class="pull_left userpic_wrap">
//                                    <div class="userpic userpic8" style="width: 42px; height: 42px">
//                                        <div class="initials" style="line-height: 42px">%s</div>
//                                    </div>
//                                </div>
//                                <div class="body">
//                                    <div class="pull_right date details" title="30.05.2025 15:58:21 UTC+08:00">15:58</div>
//                                    <div class="from_name">%s</div>
//                                    <div class="text">%s</div>
//                                </div>
//                            </div>
//                        </div>
//                    </div>
//                </div>
//            </body>
//            </html>
//            """, groupName, messageId, fromName.substring(0, 1), fromName, text);
//    }
//}