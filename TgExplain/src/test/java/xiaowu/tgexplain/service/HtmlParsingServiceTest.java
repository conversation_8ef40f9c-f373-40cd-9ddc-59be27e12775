//package xiaowu.tgexplain.service;
//
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.DisplayName;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//import org.mockito.junit.jupiter.MockitoSettings;
//import org.mockito.quality.Strictness;
//import org.springframework.mock.web.MockMultipartFile;
//import xiaowu.tgexplain.model.ChatMessage;
//import xiaowu.tgexplain.model.Message;
//import xiaowu.tgexplain.testdata.TestDataBuilder;
//
//import java.io.IOException;
//import java.nio.charset.StandardCharsets;
//
//import static org.assertj.core.api.Assertions.*;
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.Mockito.*;
//
//@ExtendWith(MockitoExtension.class)
//@MockitoSettings(strictness = Strictness.LENIENT)
//@DisplayName("HtmlParsingService Unit Tests")
//class HtmlParsingServiceTest {
//
//    @Mock
//    private DatabaseStorageService storageService;
//
//    @InjectMocks
//    private HtmlParsingService htmlParsingService;
//
//    @BeforeEach
//    void setUp() {
//        // Setup common mock behavior
//        doNothing().when(storageService).storeChatMessage(any(ChatMessage.class));
//    }
//
//    @Test
//    @DisplayName("Should parse group name correctly")
//    void shouldParseGroupNameCorrectly() throws IOException {
//        // Given
//        String htmlContent = TestDataBuilder.createSampleHtmlContent(
//            "正大拿现8群 供绿茶",
//            "message9145",
//            "绿茶",
//            "测试消息"
//        );
//        MockMultipartFile file = new MockMultipartFile(
//            "file",
//            "test.html",
//            "text/html",
//            htmlContent.getBytes(StandardCharsets.UTF_8)
//        );
//
//        // When
//        ChatMessage result = htmlParsingService.parseChatMessages(file);
//
//        // Then
//        assertThat(result).isNotNull();
//        assertThat(result.getGroupName()).isEqualTo("正大拿现8群 供绿茶");
//        verify(storageService, times(1)).storeChatMessage(result);
//    }
//
//    @Test
//    @DisplayName("Should parse service messages correctly")
//    void shouldParseServiceMessagesCorrectly() throws IOException {
//        // Given
//        String htmlContent = """
//            <!DOCTYPE html>
//            <html>
//            <head><meta charset="utf-8"/><title>Test</title></head>
//            <body>
//                <div class="page_wrap">
//                    <div class="page_header">
//                        <div class="content block_link">
//                            <div class="text bold">测试群组</div>
//                        </div>
//                    </div>
//                    <div class="page_body chat_page">
//                        <div class="history">
//                            <div class="message service" id="message-1">
//                                <div class="body details">30 May 2025</div>
//                            </div>
//                        </div>
//                    </div>
//                </div>
//            </body>
//            </html>
//            """;
//        MockMultipartFile file = new MockMultipartFile(
//            "file",
//            "test.html",
//            "text/html",
//            htmlContent.getBytes(StandardCharsets.UTF_8)
//        );
//
//        // When
//        ChatMessage result = htmlParsingService.parseChatMessages(file);
//
//        // Then
//        assertThat(result.getMessages()).hasSize(1);
//        Message serviceMessage = result.getMessages().get(0);
//        assertThat(serviceMessage.getType()).isEqualTo("service");
//        assertThat(serviceMessage.getDateDetails()).isEqualTo("30 May 2025");
//        assertThat(serviceMessage.getId()).isEqualTo("message-1");
//    }
//
//    @Test
//    @DisplayName("Should parse default messages with user information")
//    void shouldParseDefaultMessagesWithUserInformation() throws IOException {
//        // Given
//        String htmlContent = """
//            <!DOCTYPE html>
//            <html>
//            <head><meta charset="utf-8"/><title>Test</title></head>
//            <body>
//                <div class="page_wrap">
//                    <div class="page_header">
//                        <div class="content block_link">
//                            <div class="text bold">测试群组</div>
//                        </div>
//                    </div>
//                    <div class="page_body chat_page">
//                        <div class="history">
//                            <div class="message default clearfix" id="message9145">
//                                <div class="pull_left userpic_wrap">
//                                    <div class="userpic8 userpic" style="width: 42px; height: 42px">
//                                        <div class="initials" style="line-height: 42px">绿</div>
//                                    </div>
//                                </div>
//                                <div class="body">
//                                    <div class="pull_right date details" title="30.05.2025 15:58:21 UTC+08:00">15:58</div>
//                                    <div class="from_name">绿茶</div>
//                                    <div class="text">测试消息内容</div>
//                                </div>
//                            </div>
//                        </div>
//                    </div>
//                </div>
//            </body>
//            </html>
//            """;
//        MockMultipartFile file = new MockMultipartFile(
//            "file",
//            "test.html",
//            "text/html",
//            htmlContent.getBytes(StandardCharsets.UTF_8)
//        );
//
//        // When
//        ChatMessage result = htmlParsingService.parseChatMessages(file);
//
//        // Then
//        assertThat(result.getMessages()).hasSize(1);
//        Message message = result.getMessages().get(0);
//        assertThat(message.getType()).isEqualTo("default");
//        assertThat(message.getId()).isEqualTo("message9145");
//        assertThat(message.getFromName()).isEqualTo("绿茶");
//        assertThat(message.getUserInitial()).isEqualTo("绿");
//        assertThat(message.getUserPicColorClass()).isEqualTo("userpic8");
//        assertThat(message.getText()).isEqualTo("测试消息内容");
//        assertThat(message.getTime()).isEqualTo("15:58");
//        assertThat(message.getTimestamp()).isEqualTo("30.05.2025 15:58:21 UTC+08:00");
//        assertThat(message.isJoined()).isFalse();
//    }
//
//    @Test
//    @DisplayName("Should parse joined messages correctly")
//    void shouldParseJoinedMessagesCorrectly() throws IOException {
//        // Given
//        String htmlContent = """
//            <!DOCTYPE html>
//            <html>
//            <head><meta charset="utf-8"/><title>Test</title></head>
//            <body>
//                <div class="page_wrap">
//                    <div class="page_header">
//                        <div class="content block_link">
//                            <div class="text bold">测试群组</div>
//                        </div>
//                    </div>
//                    <div class="page_body chat_page">
//                        <div class="history">
//                            <div class="message default clearfix" id="message1">
//                                <div class="pull_left userpic_wrap">
//                                    <div class="userpic8 userpic">
//                                        <div class="initials">绿</div>
//                                    </div>
//                                </div>
//                                <div class="body">
//                                    <div class="pull_right date details" title="30.05.2025 15:58:21 UTC+08:00">15:58</div>
//                                    <div class="from_name">绿茶</div>
//                                    <div class="text">第一条消息</div>
//                                </div>
//                            </div>
//                            <div class="message default clearfix joined" id="message2">
//                                <div class="body">
//                                    <div class="pull_right date details" title="30.05.2025 15:59:00 UTC+08:00">15:59</div>
//                                    <div class="text">连续消息</div>
//                                </div>
//                            </div>
//                        </div>
//                    </div>
//                </div>
//            </body>
//            </html>
//            """;
//        MockMultipartFile file = new MockMultipartFile(
//            "file",
//            "test.html",
//            "text/html",
//            htmlContent.getBytes(StandardCharsets.UTF_8)
//        );
//
//        // When
//        ChatMessage result = htmlParsingService.parseChatMessages(file);
//
//        // Then
//        assertThat(result.getMessages()).hasSize(2);
//
//        Message firstMessage = result.getMessages().get(0);
//        assertThat(firstMessage.isJoined()).isFalse();
//        assertThat(firstMessage.getFromName()).isEqualTo("绿茶");
//
//        Message joinedMessage = result.getMessages().get(1);
//        assertThat(joinedMessage.isJoined()).isTrue();
//        assertThat(joinedMessage.getFromName()).isEqualTo("绿茶"); // Inherited from previous message
//        assertThat(joinedMessage.getUserInitial()).isEqualTo("绿"); // Inherited from previous message
//        assertThat(joinedMessage.getUserPicColorClass()).isEqualTo("userpic8"); // Inherited from previous message
//    }
//
//    @Test
//    @DisplayName("Should parse messages with replies")
//    void shouldParseMessagesWithReplies() throws IOException {
//        // Given
//        String htmlContent = """
//            <!DOCTYPE html>
//            <html>
//            <head><meta charset="utf-8"/><title>Test</title></head>
//            <body>
//                <div class="page_wrap">
//                    <div class="page_header">
//                        <div class="content block_link">
//                            <div class="text bold">测试群组</div>
//                        </div>
//                    </div>
//                    <div class="page_body chat_page">
//                        <div class="history">
//                            <div class="message default clearfix" id="message9145">
//                                <div class="body">
//                                    <div class="pull_right date details" title="30.05.2025 15:58:21 UTC+08:00">15:58</div>
//                                    <div class="from_name">绿茶</div>
//                                    <div class="reply_to">
//                                        <a href="#go_to_message9144" onclick="return GoToMessage(9144)">
//                                            In reply to this message
//                                        </a>
//                                    </div>
//                                    <div class="text">这是回复消息</div>
//                                </div>
//                            </div>
//                        </div>
//                    </div>
//                </div>
//            </body>
//            </html>
//            """;
//        MockMultipartFile file = new MockMultipartFile(
//            "file",
//            "test.html",
//            "text/html",
//            htmlContent.getBytes(StandardCharsets.UTF_8)
//        );
//
//        // When
//        ChatMessage result = htmlParsingService.parseChatMessages(file);
//
//        // Then
//        assertThat(result.getMessages()).hasSize(1);
//        Message message = result.getMessages().get(0);
//        assertThat(message.getReplyToMessageId()).isEqualTo("9144");
//        assertThat(message.getText()).isEqualTo("这是回复消息");
//    }
//
//    @Test
//    @DisplayName("Should parse messages with images")
//    void shouldParseMessagesWithImages() throws IOException {
//        // Given
//        String htmlContent = """
//            <!DOCTYPE html>
//            <html>
//            <head><meta charset="utf-8"/><title>Test</title></head>
//            <body>
//                <div class="page_wrap">
//                    <div class="page_header">
//                        <div class="content block_link">
//                            <div class="text bold">测试群组</div>
//                        </div>
//                    </div>
//                    <div class="page_body chat_page">
//                        <div class="history">
//                            <div class="message default clearfix" id="message9145">
//                                <div class="body">
//                                    <div class="pull_right date details" title="30.05.2025 15:58:21 UTC+08:00">15:58</div>
//                                    <div class="from_name">绿茶</div>
//                                    <div class="text">图片消息</div>
//                                    <div class="media_wrap">
//                                        <a href="photos/photo_1@30-05-2025_15-58-21.jpg" class="photo_wrap">
//                                            <img src="photos/photo_1@30-05-2025_15-58-21_thumb.jpg" class="photo" />
//                                        </a>
//                                    </div>
//                                </div>
//                            </div>
//                        </div>
//                    </div>
//                </div>
//            </body>
//            </html>
//            """;
//        MockMultipartFile file = new MockMultipartFile(
//            "file",
//            "test.html",
//            "text/html",
//            htmlContent.getBytes(StandardCharsets.UTF_8)
//        );
//
//        // When
//        ChatMessage result = htmlParsingService.parseChatMessages(file);
//
//        // Then
//        assertThat(result.getMessages()).hasSize(1);
//        Message message = result.getMessages().get(0);
//        assertThat(message.getImageUrl()).isEqualTo("photos/photo_1@30-05-2025_15-58-21.jpg");
//        assertThat(message.getThumbnailUrl()).isEqualTo("photos/photo_1@30-05-2025_15-58-21_thumb.jpg");
//    }
//
//    @Test
//    @DisplayName("Should parse messages with reactions")
//    void shouldParseMessagesWithReactions() throws IOException {
//        // Given
//        String htmlContent = """
//            <!DOCTYPE html>
//            <html>
//            <head><meta charset="utf-8"/><title>Test</title></head>
//            <body>
//                <div class="page_wrap">
//                    <div class="page_header">
//                        <div class="content block_link">
//                            <div class="text bold">测试群组</div>
//                        </div>
//                    </div>
//                    <div class="page_body chat_page">
//                        <div class="history">
//                            <div class="message default clearfix" id="message9145">
//                                <div class="body">
//                                    <div class="pull_right date details" title="30.05.2025 15:58:21 UTC+08:00">15:58</div>
//                                    <div class="from_name">绿茶</div>
//                                    <div class="text">有反应的消息</div>
//                                    <div class="reactions">
//                                        <div class="reaction">
//                                            <div class="emoji">👍</div>
//                                            <div class="userpic">
//                                                <div class="initials" title="用户1">A</div>
//                                            </div>
//                                        </div>
//                                        <div class="reaction">
//                                            <div class="emoji">❤️</div>
//                                            <div class="userpic">
//                                                <div class="initials" title="用户2">B</div>
//                                            </div>
//                                        </div>
//                                    </div>
//                                </div>
//                            </div>
//                        </div>
//                    </div>
//                </div>
//            </body>
//            </html>
//            """;
//        MockMultipartFile file = new MockMultipartFile(
//            "file",
//            "test.html",
//            "text/html",
//            htmlContent.getBytes(StandardCharsets.UTF_8)
//        );
//
//        // When
//        ChatMessage result = htmlParsingService.parseChatMessages(file);
//
//        // Then
//        assertThat(result.getMessages()).hasSize(1);
//        Message message = result.getMessages().get(0);
//        assertThat(message.getReactions()).hasSize(2);
//
//        assertThat(message.getReactions().get(0).getEmoji()).isEqualTo("👍");
//        assertThat(message.getReactions().get(0).getFromName()).isEqualTo("用户1");
//        assertThat(message.getReactions().get(0).getUserInitial()).isEqualTo("A");
//
//        assertThat(message.getReactions().get(1).getEmoji()).isEqualTo("❤️");
//        assertThat(message.getReactions().get(1).getFromName()).isEqualTo("用户2");
//        assertThat(message.getReactions().get(1).getUserInitial()).isEqualTo("B");
//    }
//
//    @Test
//    @DisplayName("Should handle empty HTML gracefully")
//    void shouldHandleEmptyHtmlGracefully() throws IOException {
//        // Given
//        String htmlContent = """
//            <!DOCTYPE html>
//            <html>
//            <head><meta charset="utf-8"/><title>Test</title></head>
//            <body>
//                <div class="page_wrap">
//                    <div class="page_header">
//                        <div class="content block_link">
//                            <div class="text bold">空群组</div>
//                        </div>
//                    </div>
//                    <div class="page_body chat_page">
//                        <div class="history">
//                        </div>
//                    </div>
//                </div>
//            </body>
//            </html>
//            """;
//        MockMultipartFile file = new MockMultipartFile(
//            "file",
//            "empty.html",
//            "text/html",
//            htmlContent.getBytes(StandardCharsets.UTF_8)
//        );
//
//        // When
//        ChatMessage result = htmlParsingService.parseChatMessages(file);
//
//        // Then
//        assertThat(result).isNotNull();
//        assertThat(result.getGroupName()).isEqualTo("空群组");
//        assertThat(result.getMessages()).isEmpty();
//    }
//
//    @Test
//    @DisplayName("Should handle malformed HTML gracefully")
//    void shouldHandleMalformedHtmlGracefully() throws IOException {
//        // Given
//        String malformedHtml = "<html><body>invalid html</body></html>";
//        MockMultipartFile file = new MockMultipartFile(
//            "file",
//            "malformed.html",
//            "text/html",
//            malformedHtml.getBytes(StandardCharsets.UTF_8)
//        );
//
//        // When & Then
//        assertThatCode(() -> htmlParsingService.parseChatMessages(file))
//            .doesNotThrowAnyException();
//    }
//
//    @Test
//    @DisplayName("Should handle IOException from file reading")
//    void shouldHandleIOExceptionFromFileReading() throws IOException {
//        // Given
//        MockMultipartFile file = mock(MockMultipartFile.class);
//        when(file.getInputStream()).thenThrow(new IOException("File read error"));
//
//        // When & Then
//        assertThatThrownBy(() -> htmlParsingService.parseChatMessages(file))
//            .isInstanceOf(IOException.class)
//            .hasMessage("File read error");
//    }
//
//    @Test
//    @DisplayName("Should parse pinned messages correctly")
//    void shouldParsePinnedMessagesCorrectly() throws IOException {
//        // Given
//        String htmlContent = """
//            <!DOCTYPE html>
//            <html>
//            <head><meta charset="utf-8"/><title>Test</title></head>
//            <body>
//                <div class="page_wrap">
//                    <div class="page_header">
//                        <div class="content block_link">
//                            <div class="text bold">测试群组</div>
//                        </div>
//                    </div>
//                    <div class="page_body chat_page">
//                        <div class="history">
//                            <div class="message service" id="pinMessage">
//                                <div class="body details">
//                                    绿茶 pinned
//                                    <a href="#go_to_message9249" onclick="return GoToMessage(9249)">
//                                        "重要消息"
//                                    </a>
//                                </div>
//                            </div>
//                        </div>
//                    </div>
//                </div>
//            </body>
//            </html>
//            """;
//        MockMultipartFile file = new MockMultipartFile(
//            "file",
//            "pinned.html",
//            "text/html",
//            htmlContent.getBytes(StandardCharsets.UTF_8)
//        );
//
//        // When
//        ChatMessage result = htmlParsingService.parseChatMessages(file);
//
//        // Then
//        assertThat(result.getMessages()).hasSize(1);
//        Message message = result.getMessages().get(0);
//        assertThat(message.getType()).isEqualTo("service");
//        assertThat(message.getPinnedMessageId()).isEqualTo("9249");
//        assertThat(message.getPinnedMessageText()).contains("绿茶 pinned");
//    }
//
//    @Test
//    @DisplayName("Should verify storage service is called with parsed data")
//    void shouldVerifyStorageServiceIsCalledWithParsedData() throws IOException {
//        // Given
//        String htmlContent = TestDataBuilder.createSampleHtmlContent(
//            "测试群组",
//            "message1",
//            "测试用户",
//            "测试消息"
//        );
//        MockMultipartFile file = new MockMultipartFile(
//            "file",
//            "test.html",
//            "text/html",
//            htmlContent.getBytes(StandardCharsets.UTF_8)
//        );
//
//        // When
//        ChatMessage result = htmlParsingService.parseChatMessages(file);
//
//        // Then
//        verify(storageService, times(1)).storeChatMessage(argThat(chatMessage -> {
//            assertThat(chatMessage.getGroupName()).isEqualTo("测试群组");
//            assertThat(chatMessage.getMessages()).hasSize(2); // Service message + default message
//            return true;
//        }));
//    }
//}