package xiaowu.tgexplain.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.springframework.mock.web.MockMultipartFile;
import xiaowu.tgexplain.config.FileStorageConfig;
import xiaowu.tgexplain.exception.FileStorageException;

import java.nio.file.Files;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 文件存储服务测试类
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
class FileStorageServiceTest {
    
    @TempDir
    Path tempDir;
    
    private FileStorageService fileStorageService;
    private FileStorageConfig fileStorageConfig;
    
    @BeforeEach
    void setUp() {
        fileStorageConfig = new FileStorageConfig();
        fileStorageConfig.setRootPath(tempDir.toString());
        fileStorageConfig.setCreateDateDirectories(true);
        fileStorageConfig.setCreateGroupDirectories(true);
        fileStorageConfig.setFilePrefix("test_");
        fileStorageConfig.setIncludeTimestamp(true);
        fileStorageConfig.setPreserveOriginalName(true);
        
        fileStorageService = new FileStorageService(fileStorageConfig);
    }
    
    @Test
    void testSaveHtmlFile_Success() throws Exception {
        // Given
        String htmlContent = "<html><body><h1>Test Chat</h1></body></html>";
        MockMultipartFile file = new MockMultipartFile(
                "file", 
                "test_chat.html", 
                "text/html", 
                htmlContent.getBytes()
        );
        String groupName = "测试群组";
        
        // When
        String savedPath = fileStorageService.saveHtmlFile(file, groupName);
        
        // Then
        assertNotNull(savedPath);
        assertTrue(fileStorageService.fileExists(savedPath));
        
        // 验证文件内容
        Path absolutePath = fileStorageService.getAbsolutePath(savedPath);
        String savedContent = Files.readString(absolutePath);
        assertEquals(htmlContent, savedContent);
    }
    
    @Test
    void testSaveHtmlFile_EmptyFile() {
        // Given
        MockMultipartFile emptyFile = new MockMultipartFile(
                "file", 
                "empty.html", 
                "text/html", 
                new byte[0]
        );
        
        // When & Then
        assertThrows(FileStorageException.class, () -> {
            fileStorageService.saveHtmlFile(emptyFile, "test_group");
        });
    }
    
    @Test
    void testSaveHtmlFile_InvalidExtension() {
        // Given
        MockMultipartFile txtFile = new MockMultipartFile(
                "file", 
                "test.txt", 
                "text/plain", 
                "test content".getBytes()
        );
        
        // When & Then
        assertThrows(FileStorageException.class, () -> {
            fileStorageService.saveHtmlFile(txtFile, "test_group");
        });
    }
    
    @Test
    void testFileExists() throws Exception {
        // Given
        String htmlContent = "<html><body>Test</body></html>";
        MockMultipartFile file = new MockMultipartFile(
                "file", 
                "exists_test.html", 
                "text/html", 
                htmlContent.getBytes()
        );
        
        // When
        String savedPath = fileStorageService.saveHtmlFile(file, "test_group");
        
        // Then
        assertTrue(fileStorageService.fileExists(savedPath));
        assertFalse(fileStorageService.fileExists("non_existent_file.html"));
    }
    
    @Test
    void testDeleteFile() throws Exception {
        // Given
        String htmlContent = "<html><body>Delete Test</body></html>";
        MockMultipartFile file = new MockMultipartFile(
                "file", 
                "delete_test.html", 
                "text/html", 
                htmlContent.getBytes()
        );
        String savedPath = fileStorageService.saveHtmlFile(file, "test_group");
        
        // When
        boolean deleted = fileStorageService.deleteFile(savedPath);
        
        // Then
        assertTrue(deleted);
        assertFalse(fileStorageService.fileExists(savedPath));
    }
    
    @Test
    void testSanitizeFileName() {
        // Test cases for file name sanitization
        assertEquals("test_file.html", 
                fileStorageConfig.sanitizeFileName("test:file.html"));
        assertEquals("test_file.html", 
                fileStorageConfig.sanitizeFileName("test\\file.html"));
        assertEquals("test_file.html", 
                fileStorageConfig.sanitizeFileName("test/file.html"));
        assertEquals("unnamed_file", 
                fileStorageConfig.sanitizeFileName(""));
        assertEquals("unnamed_file", 
                fileStorageConfig.sanitizeFileName(null));
    }
    
    @Test
    void testSanitizeGroupName() {
        // Test cases for group name sanitization
        assertEquals("test_group", 
                fileStorageConfig.sanitizeGroupName("Test Group"));
        assertEquals("test_group", 
                fileStorageConfig.sanitizeGroupName("test:group"));
        assertEquals("unknown_group", 
                fileStorageConfig.sanitizeGroupName(""));
        assertEquals("unknown_group", 
                fileStorageConfig.sanitizeGroupName(null));
    }
}
