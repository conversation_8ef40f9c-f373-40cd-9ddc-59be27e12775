//package xiaowu.tgexplain.service;
//
//import com.fasterxml.jackson.core.JsonProcessingException;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.DisplayName;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.ArgumentCaptor;
//import org.mockito.Captor;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//import org.mockito.junit.jupiter.MockitoSettings;
//import org.mockito.quality.Strictness;
//import org.springframework.mock.web.MockMultipartFile;
//import xiaowu.tgexplain.ChatParser;
//import xiaowu.tgexplain.entity.ChatEntity;
//import xiaowu.tgexplain.entity.ChatUserEntity;
//import xiaowu.tgexplain.entity.MessageEntity;
//import xiaowu.tgexplain.entity.ReactionEntity;
//import xiaowu.tgexplain.model.ChatMessage;
//import xiaowu.tgexplain.model.Message;
//import xiaowu.tgexplain.model.Reaction;
//import xiaowu.tgexplain.repository.ChatRepository;
//import xiaowu.tgexplain.repository.ChatUserRepository;
//import xiaowu.tgexplain.repository.MessageRepository;
//import xiaowu.tgexplain.testdata.TestDataBuilder;
//
//import java.io.IOException;
//import java.nio.charset.StandardCharsets;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Optional;
//
//import static org.assertj.core.api.Assertions.*;
//import static org.mockito.ArgumentMatchers.*;
//import static org.mockito.Mockito.*;
//
//@ExtendWith(MockitoExtension.class)
//@MockitoSettings(strictness = Strictness.LENIENT)
//@DisplayName("DatabaseStorageService Unit Tests")
//class DatabaseStorageServiceTest {
//
//    @Mock
//    private ChatRepository chatRepository;
//
//    @Mock
//    private ChatUserRepository chatUserRepository;
//
//    @Mock
//    private MessageRepository messageRepository;
//
//    @Mock
//    private ObjectMapper objectMapper;
//
//    @InjectMocks
//    private DatabaseStorageService databaseStorageService;
//
//    @Captor
//    private ArgumentCaptor<ChatEntity> chatEntityCaptor;
//
//    @Captor
//    private ArgumentCaptor<ChatUserEntity> userEntityCaptor;
//
//    @Captor
//    private ArgumentCaptor<MessageEntity> messageEntityCaptor;
//
//    private ChatEntity testChat;
//    private ChatUserEntity testUser;
//    private ChatMessage testChatMessage;
//
//    @BeforeEach
//    void setUp() {
//        testChat = TestDataBuilder.chatEntity()
//            .withId(1L)
//            .withGroupName("测试群组")
//            .build();
//
//        testUser = TestDataBuilder.chatUser()
//            .withId(1L)
//            .withName("测试用户")
//            .withUserInitial("测")
//            .withUserPicColorClass("userpic8")
//            .build();
//
//        // Setup default mock behaviors
//        when(chatRepository.findByGroupName(anyString())).thenReturn(Optional.empty());
//        when(chatRepository.save(any(ChatEntity.class))).thenReturn(testChat);
//        when(chatUserRepository.findByName(anyString())).thenReturn(Optional.empty());
//        when(chatUserRepository.save(any(ChatUserEntity.class))).thenReturn(testUser);
//        when(messageRepository.existsByBusinessId(anyString())).thenReturn(false);
//        when(messageRepository.save(any(MessageEntity.class))).thenAnswer(invocation -> {
//            MessageEntity message = invocation.getArgument(0);
//            if (message.getId() == null) {
//                message.setId(1L);
//            }
//            return message;
//        });
//    }
//
//    @Test
//    @DisplayName("Should create new chat when chat doesn't exist")
//    void shouldCreateNewChatWhenChatDoesntExist() {
//        // Given
//        testChatMessage = TestDataBuilder.chatMessage()
//            .withGroupName("新群组")
//            .withMessages(new ArrayList<>())
//            .build();
//
//        when(chatRepository.findByGroupName("新群组")).thenReturn(Optional.empty());
//
//        // When
//        databaseStorageService.storeChatMessage(testChatMessage);
//
//        // Then
//        verify(chatRepository, times(1)).save(chatEntityCaptor.capture());
//        ChatEntity savedChat = chatEntityCaptor.getValue();
//        assertThat(savedChat.getGroupName()).isEqualTo("新群组");
//    }
//
//    @Test
//    @DisplayName("Should use existing chat when chat exists")
//    void shouldUseExistingChatWhenChatExists() {
//        // Given
//        testChatMessage = TestDataBuilder.chatMessage()
//            .withGroupName("测试群组")
//            .withMessages(new ArrayList<>())
//            .build();
//
//        when(chatRepository.findByGroupName("测试群组")).thenReturn(Optional.of(testChat));
//
//        // When
//        databaseStorageService.storeChatMessage(testChatMessage);
//
//        // Then
//        verify(chatRepository, never()).save(any(ChatEntity.class));
//        verify(chatRepository, times(1)).findByGroupName("测试群组");
//    }
//
//    @Test
//    @DisplayName("Should store service message correctly")
//    void shouldStoreServiceMessageCorrectly() {
//        // Given
//        Message serviceMessage = TestDataBuilder.message()
//            .withId("service1")
//            .withType("service")
//            .withDateDetails("30 May 2025")
//            .build();
//
//        testChatMessage = TestDataBuilder.chatMessage()
//            .withGroupName("测试群组")
//            .addMessage(serviceMessage)
//            .build();
//
//        when(chatRepository.findByGroupName("测试群组")).thenReturn(Optional.of(testChat));
//
//        // When
//        databaseStorageService.storeChatMessage(testChatMessage);
//
//        // Then
//        verify(messageRepository, times(1)).save(messageEntityCaptor.capture());
//        MessageEntity savedMessage = messageEntityCaptor.getValue();
//
//        assertThat(savedMessage.getBusinessId()).isEqualTo("service1");
//        assertThat(savedMessage.getMessageType()).isEqualTo("service");
//        assertThat(savedMessage.getDateDetails()).isEqualTo("30 May 2025");
//        assertThat(savedMessage.getChat()).isEqualTo(testChat);
//        assertThat(savedMessage.getFromUser()).isNull();
//    }
//
//    @Test
//    @DisplayName("Should store default message with user correctly")
//    void shouldStoreDefaultMessageWithUserCorrectly() {
//        // Given
//        Message defaultMessage = TestDataBuilder.message()
//            .withId("message1")
//            .withType("default")
//            .withText("测试消息")
//            .withFromName("测试用户")
//            .withUserInitial("测")
//            .withUserPicColorClass("userpic8")
//            .withTime("15:58")
//            .withTimestamp("30.05.2025 15:58:21 UTC+08:00")
//            .withJoined(false)
//            .build();
//
//        testChatMessage = TestDataBuilder.chatMessage()
//            .withGroupName("测试群组")
//            .addMessage(defaultMessage)
//            .build();
//
//        when(chatRepository.findByGroupName("测试群组")).thenReturn(Optional.of(testChat));
//        when(chatUserRepository.findByName("测试用户")).thenReturn(Optional.empty());
//
//        // When
//        databaseStorageService.storeChatMessage(testChatMessage);
//
//        // Then
//        verify(chatUserRepository, times(1)).save(userEntityCaptor.capture());
//        ChatUserEntity savedUser = userEntityCaptor.getValue();
//        assertThat(savedUser.getName()).isEqualTo("测试用户");
//        assertThat(savedUser.getUserInitial()).isEqualTo("测");
//        assertThat(savedUser.getUserPicColorClass()).isEqualTo("userpic8");
//
//        verify(messageRepository, times(1)).save(messageEntityCaptor.capture());
//        MessageEntity savedMessage = messageEntityCaptor.getValue();
//        assertThat(savedMessage.getBusinessId()).isEqualTo("message1");
//        assertThat(savedMessage.getMessageType()).isEqualTo("default");
//        assertThat(savedMessage.getText()).isEqualTo("测试消息");
//        assertThat(savedMessage.getTime()).isEqualTo("15:58");
//        assertThat(savedMessage.getTimestamp()).isEqualTo("30.05.2025 15:58:21 UTC+08:00");
//        assertThat(savedMessage.isJoined()).isFalse();
//        assertThat(savedMessage.getFromUser()).isEqualTo(testUser);
//        assertThat(savedMessage.getChat()).isEqualTo(testChat);
//    }
//
//    @Test
//    @DisplayName("Should reuse existing user from cache")
//    void shouldReuseExistingUserFromCache() {
//        // Given
//        Message message1 = TestDataBuilder.message()
//            .withId("message1")
//            .withType("default")
//            .withFromName("测试用户")
//            .build();
//
//        Message message2 = TestDataBuilder.message()
//            .withId("message2")
//            .withType("default")
//            .withFromName("测试用户")
//            .build();
//
//        testChatMessage = TestDataBuilder.chatMessage()
//            .withGroupName("测试群组")
//            .addMessage(message1)
//            .addMessage(message2)
//            .build();
//
//        when(chatRepository.findByGroupName("测试群组")).thenReturn(Optional.of(testChat));
//        when(chatUserRepository.findByName("测试用户")).thenReturn(Optional.empty());
//
//        // When
//        databaseStorageService.storeChatMessage(testChatMessage);
//
//        // Then
//        verify(chatUserRepository, times(1)).save(any(ChatUserEntity.class)); // Only once for cache
//        verify(messageRepository, times(2)).save(any(MessageEntity.class)); // Both messages saved
//    }
//
//    @Test
//    @DisplayName("Should skip duplicate messages")
//    void shouldSkipDuplicateMessages() {
//        // Given
//        Message duplicateMessage = TestDataBuilder.message()
//            .withId("existingMessage")
//            .withType("default")
//            .withText("重复消息")
//            .build();
//
//        testChatMessage = TestDataBuilder.chatMessage()
//            .withGroupName("测试群组")
//            .addMessage(duplicateMessage)
//            .build();
//
//        when(chatRepository.findByGroupName("测试群组")).thenReturn(Optional.of(testChat));
//        when(messageRepository.existsByBusinessId("existingMessage")).thenReturn(true);
//
//        // When
//        databaseStorageService.storeChatMessage(testChatMessage);
//
//        // Then
//        verify(messageRepository, never()).save(any(MessageEntity.class));
//    }
//
//    @Test
//    @DisplayName("Should serialize forwarded message to JSON")
//    void shouldSerializeForwardedMessageToJSON() throws JsonProcessingException {
//        // Given
//        Message forwardedMessage = TestDataBuilder.message()
//            .withFromName("原发送者")
//            .withText("转发的消息")
//            .build();
//
//        Message messageWithForward = TestDataBuilder.message()
//            .withId("message1")
//            .withType("default")
//            .withText("主消息")
//            .withFromName("转发者")
//            .withForwardedMessage(forwardedMessage)
//            .build();
//
//        testChatMessage = TestDataBuilder.chatMessage()
//            .withGroupName("测试群组")
//            .addMessage(messageWithForward)
//            .build();
//
//        String expectedJson = "{\"fromName\":\"原发送者\",\"text\":\"转发的消息\"}";
//        when(chatRepository.findByGroupName("测试群组")).thenReturn(Optional.of(testChat));
//        when(chatUserRepository.findByName("转发者")).thenReturn(Optional.of(testUser));
//        when(objectMapper.writeValueAsString(forwardedMessage)).thenReturn(expectedJson);
//
//        // When
//        databaseStorageService.storeChatMessage(testChatMessage);
//
//        // Then
//        verify(objectMapper, times(1)).writeValueAsString(forwardedMessage);
//        verify(messageRepository, times(1)).save(messageEntityCaptor.capture());
//        MessageEntity savedMessage = messageEntityCaptor.getValue();
//        assertThat(savedMessage.getForwardedMessageJson()).isEqualTo(expectedJson);
//    }
//
//    @Test
//    @DisplayName("Should handle JSON serialization error gracefully")
//    void shouldHandleJSONSerializationErrorGracefully() throws JsonProcessingException {
//        // Given
//        Message forwardedMessage = TestDataBuilder.message()
//            .withFromName("原发送者")
//            .withText("转发的消息")
//            .build();
//
//        Message messageWithForward = TestDataBuilder.message()
//            .withId("message1")
//            .withType("default")
//            .withForwardedMessage(forwardedMessage)
//            .build();
//
//        testChatMessage = TestDataBuilder.chatMessage()
//            .withGroupName("测试群组")
//            .addMessage(messageWithForward)
//            .build();
//
//        when(chatRepository.findByGroupName("测试群组")).thenReturn(Optional.of(testChat));
//        when(objectMapper.writeValueAsString(forwardedMessage))
//            .thenThrow(new JsonProcessingException("Serialization error") {});
//
//        // When & Then
//        assertThatCode(() -> databaseStorageService.storeChatMessage(testChatMessage))
//            .doesNotThrowAnyException();
//
//        verify(messageRepository, times(1)).save(messageEntityCaptor.capture());
//        MessageEntity savedMessage = messageEntityCaptor.getValue();
//        assertThat(savedMessage.getForwardedMessageJson()).isNull();
//    }
//
//    @Test
//    @DisplayName("Should store message reactions correctly")
//    void shouldStoreMessageReactionsCorrectly() {
//        // Given
//        Reaction reaction1 = TestDataBuilder.reaction()
//            .withEmoji("👍")
//            .withFromName("用户1")
//            .withUserInitial("A")
//            .build();
//
//        Reaction reaction2 = TestDataBuilder.reaction()
//            .withEmoji("❤️")
//            .withFromName("用户2")
//            .withUserInitial("B")
//            .build();
//
//        Message messageWithReactions = TestDataBuilder.message()
//            .withId("message1")
//            .withType("default")
//            .withText("有反应的消息")
//            .withFromName("发送者")
//            .withReactions(List.of(reaction1, reaction2))
//            .build();
//
//        testChatMessage = TestDataBuilder.chatMessage()
//            .withGroupName("测试群组")
//            .addMessage(messageWithReactions)
//            .build();
//
//        when(chatRepository.findByGroupName("测试群组")).thenReturn(Optional.of(testChat));
//        when(chatUserRepository.findByName("发送者")).thenReturn(Optional.of(testUser));
//
//        // When
//        databaseStorageService.storeChatMessage(testChatMessage);
//
//        // Then
//        verify(messageRepository, times(2)).save(messageEntityCaptor.capture());
//        List<MessageEntity> savedMessages = messageEntityCaptor.getAllValues();
//
//        MessageEntity firstSave = savedMessages.get(0);
//        MessageEntity secondSave = savedMessages.get(1);
//
//        assertThat(secondSave.getReactions()).hasSize(2);
//        assertThat(secondSave.getReactions().get(0).getEmoji()).isEqualTo("👍");
//        assertThat(secondSave.getReactions().get(1).getEmoji()).isEqualTo("❤️");
//    }
//
//    @Test
//    @DisplayName("Should get chat messages correctly")
//    void shouldGetChatMessagesCorrectly() {
//        // Given
//        String groupName = "测试群组";
//        List<MessageEntity> expectedMessages = List.of(
//            TestDataBuilder.messageEntity().withBusinessId("msg1").build(),
//            TestDataBuilder.messageEntity().withBusinessId("msg2").build()
//        );
//
//        when(chatRepository.findByGroupName(groupName)).thenReturn(Optional.of(testChat));
//        when(messageRepository.findByChatOrderByIdAsc(testChat)).thenReturn(expectedMessages);
//
//        // When
//        List<MessageEntity> result = databaseStorageService.getChatMessages(groupName);
//
//        // Then
//        assertThat(result).hasSize(2);
//        assertThat(result).isEqualTo(expectedMessages);
//    }
//
//    @Test
//    @DisplayName("Should return empty list when chat not found")
//    void shouldReturnEmptyListWhenChatNotFound() {
//        // Given
//        String nonExistentGroupName = "不存在的群组";
//        when(chatRepository.findByGroupName(nonExistentGroupName)).thenReturn(Optional.empty());
//
//        // When
//        List<MessageEntity> result = databaseStorageService.getChatMessages(nonExistentGroupName);
//
//        // Then
//        assertThat(result).isEmpty();
//        verify(messageRepository, never()).findByChatOrderByIdAsc(any());
//    }
//
//    @Test
//    @DisplayName("Should get all chats correctly")
//    void shouldGetAllChatsCorrectly() {
//        // Given
//        List<ChatEntity> expectedChats = List.of(
//            TestDataBuilder.chatEntity().withGroupName("群组1").build(),
//            TestDataBuilder.chatEntity().withGroupName("群组2").build()
//        );
//
//        when(chatRepository.findAll()).thenReturn(expectedChats);
//
//        // When
//        List<ChatEntity> result = databaseStorageService.getAllChats();
//
//        // Then
//        assertThat(result).hasSize(2);
//        assertThat(result).isEqualTo(expectedChats);
//    }
//
//    @Test
//    @DisplayName("Should handle messages with all optional fields")
//    void shouldHandleMessagesWithAllOptionalFields() {
//        // Given
//        Message complexMessage = TestDataBuilder.message()
//            .withId("complex1")
//            .withType("default")
//            .withText("复杂消息")
//            .withFromName("用户")
//            .withTime("16:30")
//            .withTimestamp("30.05.2025 16:30:00 UTC+08:00")
//            .withReplyToMessageId("9999")
//            .withImageUrl("photos/image.jpg")
//            .withThumbnailUrl("photos/thumb.jpg")
//            .build();
//
//        testChatMessage = TestDataBuilder.chatMessage()
//            .withGroupName("测试群组")
//            .addMessage(complexMessage)
//            .build();
//
//        when(chatRepository.findByGroupName("测试群组")).thenReturn(Optional.of(testChat));
//        when(chatUserRepository.findByName("用户")).thenReturn(Optional.of(testUser));
//
//        // When
//        databaseStorageService.storeChatMessage(testChatMessage);
//
//        // Then
//        verify(messageRepository, times(1)).save(messageEntityCaptor.capture());
//        MessageEntity savedMessage = messageEntityCaptor.getValue();
//
//        assertThat(savedMessage.getBusinessId()).isEqualTo("complex1");
//        assertThat(savedMessage.getText()).isEqualTo("复杂消息");
//        assertThat(savedMessage.getTime()).isEqualTo("16:30");
//        assertThat(savedMessage.getTimestamp()).isEqualTo("30.05.2025 16:30:00 UTC+08:00");
//        assertThat(savedMessage.getReplyToMessageBusinessId()).isEqualTo("9999");
//        assertThat(savedMessage.getImageUrl()).isEqualTo("photos/image.jpg");
//        assertThat(savedMessage.getThumbnailUrl()).isEqualTo("photos/thumb.jpg");
//        assertThat(savedMessage.getFromUser()).isEqualTo(testUser);
//    }
//
//    // Note: processHtmlFile method uses external ChatParser dependency
//    // This is better tested in integration tests
//}