//package xiaowu.tgexplain.repository;
//
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.DisplayName;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
//import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
//import org.springframework.test.context.ActiveProfiles;
//import xiaowu.tgexplain.entity.ChatEntity;
//import xiaowu.tgexplain.entity.ChatUserEntity;
//import xiaowu.tgexplain.entity.MessageEntity;
//import xiaowu.tgexplain.entity.ReactionEntity;
//import xiaowu.tgexplain.testdata.TestDataBuilder;
//
//import java.util.List;
//
//import static org.assertj.core.api.Assertions.*;
//
//@DataJpaTest
//@ActiveProfiles("test")
//@DisplayName("MessageRepository Integration Tests")
//class MessageRepositoryTest {
//
//    @Autowired
//    private TestEntityManager entityManager;
//
//    @Autowired
//    private MessageRepository messageRepository;
//
//    private ChatEntity testChat;
//    private ChatUserEntity testUser;
//
//    @BeforeEach
//    void setUp() {
//        testChat = TestDataBuilder.chatEntity()
//            .withGroupName("测试群组")
//            .build();
//        testChat = entityManager.persistAndFlush(testChat);
//
//        testUser = TestDataBuilder.chatUser()
//            .withName("测试用户")
//            .withUserInitial("测")
//            .withUserPicColorClass("userpic8")
//            .build();
//        testUser = entityManager.persistAndFlush(testUser);
//    }
//
//    @Test
//    @DisplayName("Should save message entity correctly")
//    void shouldSaveMessageEntityCorrectly() {
//        // Given
//        MessageEntity message = TestDataBuilder.messageEntity()
//            .withBusinessId("message123")
//            .withMessageType("default")
//            .withText("测试消息")
//            .withTime("15:30")
//            .withTimestamp("30.05.2025 15:30:00 UTC+08:00")
//            .withJoined(false)
//            .withChat(testChat)
//            .withFromUser(testUser)
//            .build();
//
//        // When
//        MessageEntity savedMessage = messageRepository.save(message);
//        entityManager.flush();
//
//        // Then
//        assertThat(savedMessage).isNotNull();
//        assertThat(savedMessage.getId()).isNotNull();
//        assertThat(savedMessage.getBusinessId()).isEqualTo("message123");
//        assertThat(savedMessage.getMessageType()).isEqualTo("default");
//        assertThat(savedMessage.getText()).isEqualTo("测试消息");
//        assertThat(savedMessage.getTime()).isEqualTo("15:30");
//        assertThat(savedMessage.getTimestamp()).isEqualTo("30.05.2025 15:30:00 UTC+08:00");
//        assertThat(savedMessage.isJoined()).isFalse();
//        assertThat(savedMessage.getChat()).isEqualTo(testChat);
//        assertThat(savedMessage.getFromUser()).isEqualTo(testUser);
//    }
//
//    @Test
//    @DisplayName("Should check if message exists by business ID")
//    void shouldCheckIfMessageExistsByBusinessId() {
//        // Given
//        MessageEntity message = TestDataBuilder.messageEntity()
//            .withBusinessId("existingMessage")
//            .withChat(testChat)
//            .build();
//        entityManager.persistAndFlush(message);
//
//        // When & Then
//        assertThat(messageRepository.existsByBusinessId("existingMessage")).isTrue();
//        assertThat(messageRepository.existsByBusinessId("nonExistentMessage")).isFalse();
//    }
//
//    @Test
//    @DisplayName("Should find messages by chat ordered by ID ascending")
//    void shouldFindMessagesByChatOrderedByIdAscending() {
//        // Given
//        MessageEntity message1 = TestDataBuilder.messageEntity()
//            .withBusinessId("message001")
//            .withText("第一条消息")
//            .withChat(testChat)
//            .build();
//
//        MessageEntity message2 = TestDataBuilder.messageEntity()
//            .withBusinessId("message002")
//            .withText("第二条消息")
//            .withChat(testChat)
//            .build();
//
//        MessageEntity message3 = TestDataBuilder.messageEntity()
//            .withBusinessId("message003")
//            .withText("第三条消息")
//            .withChat(testChat)
//            .build();
//
//        // Save in different order to test ordering
//        entityManager.persistAndFlush(message2);
//        entityManager.persistAndFlush(message1);
//        entityManager.persistAndFlush(message3);
//
//        // When
//        List<MessageEntity> messages = messageRepository.findByChatOrderByIdAsc(testChat);
//
//        // Then
//        assertThat(messages).hasSize(3);
//        // Should be ordered by ID (ascending), which corresponds to save order
//        assertThat(messages.get(0).getBusinessId()).isEqualTo("message002");
//        assertThat(messages.get(1).getBusinessId()).isEqualTo("message001");
//        assertThat(messages.get(2).getBusinessId()).isEqualTo("message003");
//    }
//
//    @Test
//    @DisplayName("Should return empty list when no messages found for chat")
//    void shouldReturnEmptyListWhenNoMessagesFoundForChat() {
//        // Given
//        ChatEntity emptyChat = TestDataBuilder.chatEntity()
//            .withGroupName("空群组")
//            .build();
//        emptyChat = entityManager.persistAndFlush(emptyChat);
//
//        // When
//        List<MessageEntity> messages = messageRepository.findByChatOrderByIdAsc(emptyChat);
//
//        // Then
//        assertThat(messages).isEmpty();
//    }
//
//    @Test
//    @DisplayName("Should handle unique constraint on business ID")
//    void shouldHandleUniqueConstraintOnBusinessId() {
//        // Given
//        MessageEntity message1 = TestDataBuilder.messageEntity()
//            .withBusinessId("uniqueMessage")
//            .withText("第一条消息")
//            .withChat(testChat)
//            .build();
//
//        MessageEntity message2 = TestDataBuilder.messageEntity()
//            .withBusinessId("uniqueMessage") // Same business ID
//            .withText("第二条消息")
//            .withChat(testChat)
//            .build();
//
//        // When & Then
//        messageRepository.save(message1);
//        entityManager.flush();
//
//        assertThatThrownBy(() -> {
//            messageRepository.save(message2);
//            entityManager.flush();
//        }).isInstanceOf(Exception.class); // Should throw constraint violation
//    }
//
//    @Test
//    @DisplayName("Should save service message correctly")
//    void shouldSaveServiceMessageCorrectly() {
//        // Given
//        MessageEntity serviceMessage = TestDataBuilder.messageEntity()
//            .withBusinessId("service1")
//            .withMessageType("service")
//            .withDateDetails("30 May 2025")
//            .withChat(testChat)
//            .build();
//
//        // When
//        MessageEntity savedMessage = messageRepository.save(serviceMessage);
//        entityManager.flush();
//
//        // Then
//        assertThat(savedMessage.getMessageType()).isEqualTo("service");
//        assertThat(savedMessage.getDateDetails()).isEqualTo("30 May 2025");
//        assertThat(savedMessage.getFromUser()).isNull();
//    }
//
//    @Test
//    @DisplayName("Should save message with forwarded JSON correctly")
//    void shouldSaveMessageWithForwardedJsonCorrectly() {
//        // Given
//        String forwardedJson = "{\"fromName\":\"原发送者\",\"text\":\"转发消息\"}";
//        MessageEntity messageWithForward = TestDataBuilder.messageEntity()
//            .withBusinessId("forward1")
//            .withMessageType("default")
//            .withText("转发了一条消息")
//            .withForwardedMessageJson(forwardedJson)
//            .withChat(testChat)
//            .withFromUser(testUser)
//            .build();
//
//        // When
//        MessageEntity savedMessage = messageRepository.save(messageWithForward);
//        entityManager.flush();
//
//        // Then
//        assertThat(savedMessage.getForwardedMessageJson()).isEqualTo(forwardedJson);
//    }
//
//    @Test
//    @DisplayName("Should save message with reactions correctly")
//    void shouldSaveMessageWithReactionsCorrectly() {
//        // Given
//        MessageEntity message = TestDataBuilder.messageEntity()
//            .withBusinessId("reactedMessage")
//            .withMessageType("default")
//            .withText("有反应的消息")
//            .withChat(testChat)
//            .withFromUser(testUser)
//            .build();
//
//        // Save message first
//        message = messageRepository.save(message);
//        entityManager.flush();
//
//        // Add reactions
//        ReactionEntity reaction1 = TestDataBuilder.reactionEntity()
//            .withEmoji("👍")
//            .withMessage(message)
//            .withFromUser(testUser)
//            .build();
//
//        ReactionEntity reaction2 = TestDataBuilder.reactionEntity()
//            .withEmoji("❤️")
//            .withMessage(message)
//            .withFromUser(testUser)
//            .build();
//
//        message.setReactions(new java.util.ArrayList<>(List.of(reaction1, reaction2)));
//
//        // When
//        MessageEntity savedMessage = messageRepository.save(message);
//        entityManager.flush();
//
//        // Then
//        assertThat(savedMessage.getReactions()).hasSize(2);
//        assertThat(savedMessage.getReactions().get(0).getEmoji()).isEqualTo("👍");
//        assertThat(savedMessage.getReactions().get(1).getEmoji()).isEqualTo("❤️");
//    }
//
//    @Test
//    @DisplayName("Should handle null and empty business IDs")
//    void shouldHandleNullAndEmptyBusinessIds() {
//        // When & Then
//        assertThat(messageRepository.existsByBusinessId(null)).isFalse();
//        assertThat(messageRepository.existsByBusinessId("")).isFalse();
//        assertThat(messageRepository.existsByBusinessId("   ")).isFalse();
//    }
//
//    @Test
//    @DisplayName("Should save message with all optional fields populated")
//    void shouldSaveMessageWithAllOptionalFieldsPopulated() {
//        // Given
//        MessageEntity complexMessage = TestDataBuilder.messageEntity()
//            .withBusinessId("complex1")
//            .withMessageType("default")
//            .withText("复杂消息内容")
//            .withTime("16:45")
//            .withTimestamp("30.05.2025 16:45:30 UTC+08:00")
//            .withJoined(true)
//            .withReplyToMessageBusinessId("9999")
//            .withImageUrl("photos/image.jpg")
//            .withThumbnailUrl("photos/thumb.jpg")
//            .withPinnedMessageBusinessId("8888")
//            .withPinnedMessageText("置顶消息文本")
//            .withForwardedMessageJson("{\"text\":\"转发内容\"}")
//            .withDateDetails("服务消息详情")
//            .withChat(testChat)
//            .withFromUser(testUser)
//            .build();
//
//        // When
//        MessageEntity savedMessage = messageRepository.save(complexMessage);
//        entityManager.flush();
//
//        // Then
//        assertThat(savedMessage.getBusinessId()).isEqualTo("complex1");
//        assertThat(savedMessage.getMessageType()).isEqualTo("default");
//        assertThat(savedMessage.getText()).isEqualTo("复杂消息内容");
//        assertThat(savedMessage.getTime()).isEqualTo("16:45");
//        assertThat(savedMessage.getTimestamp()).isEqualTo("30.05.2025 16:45:30 UTC+08:00");
//        assertThat(savedMessage.isJoined()).isTrue();
//        assertThat(savedMessage.getReplyToMessageBusinessId()).isEqualTo("9999");
//        assertThat(savedMessage.getImageUrl()).isEqualTo("photos/image.jpg");
//        assertThat(savedMessage.getThumbnailUrl()).isEqualTo("photos/thumb.jpg");
//        assertThat(savedMessage.getPinnedMessageBusinessId()).isEqualTo("8888");
//        assertThat(savedMessage.getPinnedMessageText()).isEqualTo("置顶消息文本");
//        assertThat(savedMessage.getForwardedMessageJson()).isEqualTo("{\"text\":\"转发内容\"}");
//        assertThat(savedMessage.getDateDetails()).isEqualTo("服务消息详情");
//        assertThat(savedMessage.getChat()).isEqualTo(testChat);
//        assertThat(savedMessage.getFromUser()).isEqualTo(testUser);
//    }
//
//    @Test
//    @DisplayName("Should handle large text content in LOB fields")
//    void shouldHandleLargeTextContentInLobFields() {
//        // Given
//        String largeText = "大段文本内容".repeat(1000); // Create large text
//        String largeDateDetails = "大段日期详情".repeat(500);
//        String largePinnedText = "大段置顶文本".repeat(300);
//
//        MessageEntity messageWithLargeContent = TestDataBuilder.messageEntity()
//            .withBusinessId("largeContent")
//            .withMessageType("service")
//            .withText(largeText)
//            .withDateDetails(largeDateDetails)
//            .withPinnedMessageText(largePinnedText)
//            .withChat(testChat)
//            .build();
//
//        // When
//        MessageEntity savedMessage = messageRepository.save(messageWithLargeContent);
//        entityManager.flush();
//
//        // Then
//        assertThat(savedMessage.getText()).isEqualTo(largeText);
//        assertThat(savedMessage.getDateDetails()).isEqualTo(largeDateDetails);
//        assertThat(savedMessage.getPinnedMessageText()).isEqualTo(largePinnedText);
//    }
//}