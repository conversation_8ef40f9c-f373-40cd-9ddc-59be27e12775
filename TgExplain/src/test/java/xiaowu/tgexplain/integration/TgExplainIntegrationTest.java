//package xiaowu.tgexplain.integration;
//
//import com.fasterxml.jackson.databind.ObjectMapper;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.DisplayName;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.http.MediaType;
//import org.springframework.mock.web.MockMultipartFile;
//import org.springframework.test.context.ActiveProfiles;
//import org.springframework.test.context.jdbc.Sql;
//import org.springframework.test.web.servlet.MockMvc;
//import org.springframework.transaction.annotation.Transactional;
//import xiaowu.tgexplain.entity.ChatEntity;
//import xiaowu.tgexplain.entity.ChatUserEntity;
//import xiaowu.tgexplain.entity.MessageEntity;
//import xiaowu.tgexplain.model.ChatMessage;
//import xiaowu.tgexplain.model.Message;
//import xiaowu.tgexplain.repository.ChatRepository;
//import xiaowu.tgexplain.repository.ChatUserRepository;
//import xiaowu.tgexplain.repository.MessageRepository;
//import xiaowu.tgexplain.service.DatabaseStorageService;
//import xiaowu.tgexplain.service.HtmlParsingService;
//import xiaowu.tgexplain.testdata.TestDataBuilder;
//
//import java.nio.charset.StandardCharsets;
//import java.util.List;
//import java.util.Optional;
//
//import static org.assertj.core.api.Assertions.*;
//import static org.hamcrest.Matchers.*;
//import static org.hamcrest.Matchers.containsString;
//import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
//import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
//
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK)
//@AutoConfigureMockMvc
//@ActiveProfiles("test")
//@Transactional
//@DisplayName("TgExplain End-to-End Integration Tests")
//class TgExplainIntegrationTest {
//
//    @Autowired
//    private MockMvc mockMvc;
//
//    @Autowired
//    private ObjectMapper objectMapper;
//
//    @Autowired
//    private HtmlParsingService htmlParsingService;
//
//    @Autowired
//    private DatabaseStorageService databaseStorageService;
//
//    @Autowired
//    private ChatRepository chatRepository;
//
//    @Autowired
//    private ChatUserRepository chatUserRepository;
//
//    @Autowired
//    private MessageRepository messageRepository;
//
//    @BeforeEach
//    void setUp() {
//        // Clean up database before each test
//        messageRepository.deleteAll();
//        chatUserRepository.deleteAll();
//        chatRepository.deleteAll();
//    }
//
//    @Test
//    @DisplayName("Should complete full HTML parsing and storage workflow")
//    void shouldCompleteFullHtmlParsingAndStorageWorkflow() throws Exception {
//        // Given
//        String htmlContent = createCompleteHtmlContent();
//        MockMultipartFile file = new MockMultipartFile(
//            "file",
//            "complete_chat.html",
//            "text/html",
//            htmlContent.getBytes(StandardCharsets.UTF_8)
//        );
//
//        // When
//        ChatMessage parsedResult = htmlParsingService.parseChatMessages(file);
//
//        // Then - Verify parsing results
//        assertThat(parsedResult).isNotNull();
//        assertThat(parsedResult.getGroupName()).isEqualTo("集成测试群组");
//        assertThat(parsedResult.getMessages()).hasSize(4); // 1 service + 3 default messages
//
//        // Verify service message
//        Message serviceMessage = parsedResult.getMessages().get(0);
//        assertThat(serviceMessage.getType()).isEqualTo("service");
//        assertThat(serviceMessage.getDateDetails()).isEqualTo("30 May 2025");
//
//        // Verify default messages
//        Message firstMessage = parsedResult.getMessages().get(1);
//        assertThat(firstMessage.getType()).isEqualTo("default");
//        assertThat(firstMessage.getFromName()).isEqualTo("用户1");
//        assertThat(firstMessage.getText()).isEqualTo("第一条消息");
//        assertThat(firstMessage.isJoined()).isFalse();
//
//        // Verify joined message
//        Message joinedMessage = parsedResult.getMessages().get(2);
//        assertThat(joinedMessage.isJoined()).isTrue();
//        assertThat(joinedMessage.getFromName()).isEqualTo("用户1"); // Inherited
//
//        // Verify database storage
//        Optional<ChatEntity> savedChat = chatRepository.findByGroupName("集成测试群组");
//        assertThat(savedChat).isPresent();
//
//        List<MessageEntity> savedMessages = messageRepository.findByChatOrderByIdAsc(savedChat.get());
//        assertThat(savedMessages).hasSize(4);
//
//        List<ChatUserEntity> savedUsers = chatUserRepository.findAll();
//        assertThat(savedUsers).hasSize(2); // 用户1 and 用户2
//    }
//
//    @Test
//    @DisplayName("Should handle complete REST API workflow")
//    void shouldHandleCompleteRestApiWorkflow() throws Exception {
//        // Given
//        String htmlContent = createCompleteHtmlContent();
//        MockMultipartFile file = new MockMultipartFile(
//            "file",
//            "api_test.html",
//            "text/html",
//            htmlContent.getBytes(StandardCharsets.UTF_8)
//        );
//
//        // When - Upload file via API
//        mockMvc.perform(multipart("/api/chat/upload")
//                .file(file))
//            .andExpect(status().isOk())
//            .andExpect(jsonPath("$.success").value(true))
//            .andExpect(jsonPath("$.message").value(containsString("成功导入")));
//
//        // Then - Verify API responses
//        // 1. Get all chats
//        mockMvc.perform(get("/api/chat/chats"))
//            .andExpect(status().isOk())
//            .andExpect(jsonPath("$", hasSize(1)))
//            .andExpect(jsonPath("$[0].groupName").value("集成测试群组"));
//
//        // 2. Get messages for the chat
//        mockMvc.perform(get("/api/chat/messages/{groupName}", "集成测试群组"))
//            .andExpect(status().isOk())
//            .andExpect(jsonPath("$", hasSize(4)))
//            .andExpect(jsonPath("$[0].messageType").value("service"))
//            .andExpect(jsonPath("$[1].messageType").value("default"))
//            .andExpect(jsonPath("$[1].fromUserName").value("用户1"))
//            .andExpect(jsonPath("$[1].text").value("第一条消息"))
//            .andExpect(jsonPath("$[1].joined").value(false))
//            .andExpect(jsonPath("$[2].joined").value(true))
//            .andExpect(jsonPath("$[3].fromUserName").value("用户2"));
//
//        // 3. Verify health endpoint
//        mockMvc.perform(get("/api/chat/health"))
//            .andExpect(status().isOk())
//            .andExpect(content().string("TgExplain API is running"));
//    }
//
//    @Test
//    @DisplayName("Should prevent duplicate message imports")
//    void shouldPreventDuplicateMessageImports() throws Exception {
//        // Given
//        String htmlContent = createSimpleHtmlContent();
//        MockMultipartFile file = new MockMultipartFile(
//            "file",
//            "duplicate_test.html",
//            "text/html",
//            htmlContent.getBytes(StandardCharsets.UTF_8)
//        );
//
//        // When - Import the same file twice
//        mockMvc.perform(multipart("/api/chat/upload").file(file))
//            .andExpect(status().isOk());
//
//        long initialMessageCount = messageRepository.count();
//
//        mockMvc.perform(multipart("/api/chat/upload").file(file))
//            .andExpect(status().isOk());
//
//        // Then - Message count should remain the same
//        long finalMessageCount = messageRepository.count();
//        assertThat(finalMessageCount).isEqualTo(initialMessageCount);
//    }
//
//    @Test
//    @DisplayName("Should handle multiple chats correctly")
//    void shouldHandleMultipleChatsCorrectly() throws Exception {
//        // Given
//        String htmlContent1 = createHtmlContentWithGroupName("群组A");
//        String htmlContent2 = createHtmlContentWithGroupName("群组B");
//
//        MockMultipartFile file1 = new MockMultipartFile(
//            "file", "chatA.html", "text/html",
//            htmlContent1.getBytes(StandardCharsets.UTF_8)
//        );
//        MockMultipartFile file2 = new MockMultipartFile(
//            "file", "chatB.html", "text/html",
//            htmlContent2.getBytes(StandardCharsets.UTF_8)
//        );
//
//        // When - Upload multiple chat files
//        mockMvc.perform(multipart("/api/chat/upload").file(file1))
//            .andExpect(status().isOk());
//        mockMvc.perform(multipart("/api/chat/upload").file(file2))
//            .andExpect(status().isOk());
//
//        // Then - Verify both chats exist
//        mockMvc.perform(get("/api/chat/chats"))
//            .andExpect(status().isOk())
//            .andExpect(jsonPath("$", hasSize(2)))
//            .andExpect(jsonPath("$[*].groupName", containsInAnyOrder("群组A", "群组B")));
//
//        // Verify messages for each chat
//        mockMvc.perform(get("/api/chat/messages/{groupName}", "群组A"))
//            .andExpect(status().isOk())
//            .andExpect(jsonPath("$[*].chatGroupName", everyItem(is("群组A"))));
//
//        mockMvc.perform(get("/api/chat/messages/{groupName}", "群组B"))
//            .andExpect(status().isOk())
//            .andExpect(jsonPath("$[*].chatGroupName", everyItem(is("群组B"))));
//    }
//
//    @Test
//    @DisplayName("Should handle messages with reactions end-to-end")
//    void shouldHandleMessagesWithReactionsEndToEnd() throws Exception {
//        // Given
//        String htmlContentWithReactions = createHtmlContentWithReactions();
//        MockMultipartFile file = new MockMultipartFile(
//            "file",
//            "reactions_test.html",
//            "text/html",
//            htmlContentWithReactions.getBytes(StandardCharsets.UTF_8)
//        );
//
//        // When
//        mockMvc.perform(multipart("/api/chat/upload").file(file))
//            .andExpect(status().isOk());
//
//        // Then
//        mockMvc.perform(get("/api/chat/messages/{groupName}", "反应测试群组"))
//            .andExpect(status().isOk())
//            .andExpect(jsonPath("$[1].reactions", hasSize(2)))
//            .andExpect(jsonPath("$[1].reactions[0].emoji").value("👍"))
//            .andExpect(jsonPath("$[1].reactions[0].userName").value("反应用户1"))
//            .andExpect(jsonPath("$[1].reactions[1].emoji").value("❤️"))
//            .andExpect(jsonPath("$[1].reactions[1].userName").value("反应用户2"));
//    }
//
//    @Test
//    @DisplayName("Should handle error scenarios gracefully")
//    void shouldHandleErrorScenariosGracefully() throws Exception {
//        // Test 1: Empty file
//        MockMultipartFile emptyFile = new MockMultipartFile(
//            "file", "empty.html", "text/html", new byte[0]
//        );
//        mockMvc.perform(multipart("/api/chat/upload").file(emptyFile))
//            .andExpect(status().isBadRequest())
//            .andExpect(jsonPath("$.success").value(false));
//
//        // Test 2: Invalid file type
//        MockMultipartFile txtFile = new MockMultipartFile(
//            "file", "test.txt", "text/plain", "not html".getBytes()
//        );
//        mockMvc.perform(multipart("/api/chat/upload").file(txtFile))
//            .andExpect(status().isBadRequest())
//            .andExpect(jsonPath("$.success").value(false));
//
//        // Test 3: Non-existent chat messages
//        mockMvc.perform(get("/api/chat/messages/{groupName}", "不存在的群组"))
//            .andExpect(status().isOk())
//            .andExpect(jsonPath("$", hasSize(0)));
//    }
//
//    @Test
//    @DisplayName("Should verify database constraints and relationships")
//    void shouldVerifyDatabaseConstraintsAndRelationships() throws Exception {
//        // Given
//        ChatMessage chatMessage = TestDataBuilder.chatMessage()
//            .withGroupName("约束测试群组")
//            .addMessage(TestDataBuilder.message()
//                .withId("unique_msg_1")
//                .withType("default")
//                .withText("测试消息")
//                .withFromName("测试用户")
//                .build())
//            .build();
//
//        // When
//        databaseStorageService.storeChatMessage(chatMessage);
//
//        // Then - Verify entities are properly saved with relationships
//        Optional<ChatEntity> chat = chatRepository.findByGroupName("约束测试群组");
//        assertThat(chat).isPresent();
//
//        Optional<ChatUserEntity> user = chatUserRepository.findByName("测试用户");
//        assertThat(user).isPresent();
//
//        List<MessageEntity> messages = messageRepository.findByChatOrderByIdAsc(chat.get());
//        assertThat(messages).hasSize(1);
//        assertThat(messages.get(0).getChat()).isEqualTo(chat.get());
//        assertThat(messages.get(0).getFromUser()).isEqualTo(user.get());
//
//        // Verify unique constraint on business ID
//        assertThat(messageRepository.existsByBusinessId("unique_msg_1")).isTrue();
//        assertThat(messageRepository.existsByBusinessId("non_existent")).isFalse();
//    }
//
//    @Test
//    @DisplayName("Should handle large HTML files efficiently")
//    void shouldHandleLargeHtmlFilesEfficiently() throws Exception {
//        // Given - Create HTML with many messages
//        StringBuilder largeHtml = new StringBuilder();
//        largeHtml.append(createHtmlHeader("大型群组"));
//
//        // Add 100 messages
//        for (int i = 1; i <= 100; i++) {
//            largeHtml.append(createMessageHtml("message" + i, "用户" + (i % 10), "消息内容 " + i));
//        }
//
//        largeHtml.append(createHtmlFooter());
//
//        MockMultipartFile largeFile = new MockMultipartFile(
//            "file",
//            "large_chat.html",
//            "text/html",
//            largeHtml.toString().getBytes(StandardCharsets.UTF_8)
//        );
//
//        // When
//        long startTime = System.currentTimeMillis();
//        mockMvc.perform(multipart("/api/chat/upload").file(largeFile))
//            .andExpect(status().isOk())
//            .andExpect(jsonPath("$.success").value(true));
//        long endTime = System.currentTimeMillis();
//
//        // Then - Should complete in reasonable time (less than 5 seconds)
//        assertThat(endTime - startTime).isLessThan(5000);
//
//        // Verify all messages were saved
//        Optional<ChatEntity> chat = chatRepository.findByGroupName("大型群组");
//        assertThat(chat).isPresent();
//
//        List<MessageEntity> messages = messageRepository.findByChatOrderByIdAsc(chat.get());
//        assertThat(messages).hasSizeGreaterThanOrEqualTo(100);
//    }
//
//    // Helper methods for creating test HTML content
//
//    private String createCompleteHtmlContent() {
//        return """
//            <!DOCTYPE html>
//            <html>
//            <head><meta charset="utf-8"/><title>Test</title></head>
//            <body>
//                <div class="page_wrap">
//                    <div class="page_header">
//                        <div class="content block_link">
//                            <div class="text bold">集成测试群组</div>
//                        </div>
//                    </div>
//                    <div class="page_body chat_page">
//                        <div class="history">
//                            <div class="message service" id="service1">
//                                <div class="body details">30 May 2025</div>
//                            </div>
//                            <div class="message default clearfix" id="message1">
//                                <div class="pull_left userpic_wrap">
//                                    <div class="userpic userpic1">
//                                        <div class="initials">用</div>
//                                    </div>
//                                </div>
//                                <div class="body">
//                                    <div class="pull_right date details" title="30.05.2025 15:30:00 UTC+08:00">15:30</div>
//                                    <div class="from_name">用户1</div>
//                                    <div class="text">第一条消息</div>
//                                </div>
//                            </div>
//                            <div class="message default clearfix joined" id="message2">
//                                <div class="body">
//                                    <div class="pull_right date details" title="30.05.2025 15:31:00 UTC+08:00">15:31</div>
//                                    <div class="text">连续消息</div>
//                                </div>
//                            </div>
//                            <div class="message default clearfix" id="message3">
//                                <div class="pull_left userpic_wrap">
//                                    <div class="userpic userpic2">
//                                        <div class="initials">户</div>
//                                    </div>
//                                </div>
//                                <div class="body">
//                                    <div class="pull_right date details" title="30.05.2025 15:32:00 UTC+08:00">15:32</div>
//                                    <div class="from_name">用户2</div>
//                                    <div class="text">第三条消息</div>
//                                </div>
//                            </div>
//                        </div>
//                    </div>
//                </div>
//            </body>
//            </html>
//            """;
//    }
//
//    private String createSimpleHtmlContent() {
//        return TestDataBuilder.createSampleHtmlContent(
//            "简单测试群组",
//            "simple1",
//            "简单用户",
//            "简单消息"
//        );
//    }
//
//    private String createHtmlContentWithGroupName(String groupName) {
//        return TestDataBuilder.createSampleHtmlContent(
//            groupName,
//            "msg1",
//            "用户",
//            "测试消息"
//        );
//    }
//
//    private String createHtmlContentWithReactions() {
//        return """
//            <!DOCTYPE html>
//            <html>
//            <head><meta charset="utf-8"/><title>Test</title></head>
//            <body>
//                <div class="page_wrap">
//                    <div class="page_header">
//                        <div class="content block_link">
//                            <div class="text bold">反应测试群组</div>
//                        </div>
//                    </div>
//                    <div class="page_body chat_page">
//                        <div class="history">
//                            <div class="message service" id="service1">
//                                <div class="body details">30 May 2025</div>
//                            </div>
//                            <div class="message default clearfix" id="message1">
//                                <div class="body">
//                                    <div class="pull_right date details">15:30</div>
//                                    <div class="from_name">发送者</div>
//                                    <div class="text">有反应的消息</div>
//                                    <div class="reactions">
//                                        <div class="reaction">
//                                            <div class="emoji">👍</div>
//                                            <div class="userpic">
//                                                <div class="initials" title="反应用户1">A</div>
//                                            </div>
//                                        </div>
//                                        <div class="reaction">
//                                            <div class="emoji">❤️</div>
//                                            <div class="userpic">
//                                                <div class="initials" title="反应用户2">B</div>
//                                            </div>
//                                        </div>
//                                    </div>
//                                </div>
//                            </div>
//                        </div>
//                    </div>
//                </div>
//            </body>
//            </html>
//            """;
//    }
//
//    private String createHtmlHeader(String groupName) {
//        return String.format("""
//            <!DOCTYPE html>
//            <html>
//            <head><meta charset="utf-8"/><title>Large Test</title></head>
//            <body>
//                <div class="page_wrap">
//                    <div class="page_header">
//                        <div class="content block_link">
//                            <div class="text bold">%s</div>
//                        </div>
//                    </div>
//                    <div class="page_body chat_page">
//                        <div class="history">
//            """, groupName);
//    }
//
//    private String createMessageHtml(String messageId, String fromName, String text) {
//        return String.format("""
//            <div class="message default clearfix" id="%s">
//                <div class="body">
//                    <div class="pull_right date details">15:30</div>
//                    <div class="from_name">%s</div>
//                    <div class="text">%s</div>
//                </div>
//            </div>
//            """, messageId, fromName, text);
//    }
//
//    private String createHtmlFooter() {
//        return """
//                        </div>
//                    </div>
//                </div>
//            </body>
//            </html>
//            """;
//    }
//}