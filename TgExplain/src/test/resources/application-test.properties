# Test Database Configuration
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# JPA Configuration for Tests
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

# Logging Configuration for Tests
logging.level.org.springframework.test=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.xiaowu.tgexplain=DEBUG

# Test specific settings
spring.mvc.throw-exception-if-no-handler-found=true
spring.web.resources.add-mappings=false

# Disable Spring Boot banner in tests
spring.main.banner-mode=off