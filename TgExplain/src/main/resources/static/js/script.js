document.addEventListener('DOMContentLoaded', () => {
    const form = document.getElementById('upload-form');
    const fileInput = document.getElementById('file-input');
    const fileNameDisplay = document.getElementById('file-name');
    const chatContainer = document.getElementById('chat-container');
    const loadingIndicator = document.getElementById('loading-indicator');
    const themeToggleBtn = document.getElementById('theme-toggle-btn');
    const chatHeaderBar = document.getElementById('chat-header-bar');
    const groupNameDisplay = document.getElementById('group-name-display');

    // --- Theme Toggler ---
    const sunIcon = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 7c-2.76 0-5 2.24-5 5s2.24 5 5 5 5-2.24 5-5-2.24-5-5-5zM12 9c1.65 0 3 1.35 3 3s-1.35 3-3 3-3-1.35-3-3 1.35-3 3-3zm0-7c.55 0 1 .45 1 1v2c0 .55-.45 1-1 1s-1-.45-1-1V3c0-.55.45-1 1-1zm0 18c.55 0 1 .45 1 1v2c0 .55-.45 1-1 1s-1-.45-1-1v-2c0-.55.45-1 1-1zm-8-9c-.55 0-1-.45-1-1H1c-.55 0-1-.45-1-1s.45-1 1-1h2c.55 0 1 .45 1 1s-.45 1-1 1zm16 0c-.55 0-1-.45-1-1h-2c-.55 0-1-.45-1-1s.45-1 1-1h2c.55 0 1 .45 1 1s-.45 1-1 1zM5.64 5.64c-.39-.39-1.02-.39-1.41 0s-.39 1.02 0 1.41l2.12 2.12c.39.39 1.02.39 1.41 0s.39-1.02 0-1.41L5.64 5.64zm12.72 12.72c-.39-.39-1.02-.39-1.41 0s-.39 1.02 0 1.41l2.12 2.12c.39.39 1.02.39 1.41 0s.39-1.02 0-1.41l-2.12-2.12zM5.64 18.36l-2.12-2.12c-.39-.39-.39-1.02 0-1.41s1.02-.39 1.41 0l2.12 2.12c.39.39.39 1.02 0 1.41s-1.02.39-1.41 0zm12.72-12.72l-2.12-2.12c-.39-.39-.39-1.02 0-1.41s1.02-.39 1.41 0l2.12 2.12c.39.39.39 1.02 0 1.41s-1.02.39-1.41 0z"/></svg>`;
    const moonIcon = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9c.83 0 1.62-.12 2.37-.34-.34-.58-.57-1.25-.57-2.02 0-1.95 1.4-3.6 3.2-3.96.48-.1.97-.15 1.47-.15.42 0 .83.06 1.23.18-1.12-4.48-5.14-7.71-9.7-7.71z"/></svg>`;

    const currentTheme = localStorage.getItem('theme') || 'light';
    document.documentElement.setAttribute('data-theme', currentTheme);
    themeToggleBtn.innerHTML = currentTheme === 'dark' ? sunIcon : moonIcon;

    themeToggleBtn.addEventListener('click', () => {
        let theme = document.documentElement.getAttribute('data-theme');
        if (theme === 'dark') {
            theme = 'light';
            themeToggleBtn.innerHTML = moonIcon;
        } else {
            theme = 'dark';
            themeToggleBtn.innerHTML = sunIcon;
        }
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('theme', theme);
    });

    // --- File Input Handler ---
    fileInput.addEventListener('change', () => {
        if (fileInput.files.length > 0) {
            fileNameDisplay.textContent = fileInput.files[0].name;
            fileNameDisplay.style.color = 'var(--text-primary)';
        } else {
            fileNameDisplay.textContent = '未选择文件';
            fileNameDisplay.style.color = 'var(--text-secondary)';
        }
    });

    // --- Form Submission & Parsing ---
    form.addEventListener('submit', async (e) => {
        e.preventDefault();

        if (!fileInput.files || fileInput.files.length === 0) {
            alert('请先选择一个文件。');
            return;
        }

        const formData = new FormData();
        formData.append('file', fileInput.files[0]);

        loadingIndicator.style.display = 'block';
        chatHeaderBar.style.display = 'none';
        chatContainer.innerHTML = `<div class="chat-placeholder">
            <span>解析中...</span>
            <p>正在处理您的聊天记录文件</p>
        </div>`; // Clear previous results

        try {
            const response = await fetch('/api/upload', {
                method: 'POST',
                body: formData,
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`服务器错误: ${response.status} - ${errorText}`);
            }

            const data = await response.json();
            renderChat(data);

        } catch (error) {
            console.error('解析失败:', error);
            chatContainer.innerHTML = `<div class="service-message"><div class="details error">解析失败: ${error.message}</div></div>`;
        } finally {
            loadingIndicator.style.display = 'none';
        }
    });

    function renderChat(chatData) {
        chatContainer.innerHTML = ''; // Clear placeholder

        // Display group name
        if (chatData.groupName) {
            groupNameDisplay.textContent = chatData.groupName;
            chatHeaderBar.style.display = 'block';
        }

        // Render messages
        if (chatData.messages && chatData.messages.length > 0) {
            chatData.messages.forEach(msg => {
                const msgElement = createMessageElement(msg);
                if (msgElement) {
                    chatContainer.appendChild(msgElement);
                }
            });
        } else {
             chatContainer.innerHTML += '<div class="service-message"><div class="details">没有找到任何消息。</div></div>';
        }
        
        // Scroll to the top to see the first messages
        chatContainer.scrollTop = chatContainer.scrollHeight;
    }

    function createMessageElement(msg) {
        if (msg.type === 'service') {
            return createServiceMessageElement(msg);
        } else if (msg.type === 'default') {
            return createDefaultMessageElement(msg);
        }
        return null;
    }

    function createServiceMessageElement(msg) {
        const serviceDiv = document.createElement('div');
        serviceDiv.className = 'service-message';

        const detailsDiv = document.createElement('div');
        detailsDiv.className = 'details';
        detailsDiv.textContent = msg.pinnedMessageText || msg.dateDetails;
        
        serviceDiv.appendChild(detailsDiv);
        return serviceDiv;
    }

    function createDefaultMessageElement(msg) {
        const msgDiv = document.createElement('div');
        msgDiv.className = 'message default';
        if (msg.joined) {
            msgDiv.classList.add('joined');
        }

        // Avatar (only for non-joined messages)
        if (!msg.joined) {
            const avatarDiv = document.createElement('div');
            avatarDiv.className = `user-avatar ${msg.userPicColorClass || 'userpic1'}`;
            avatarDiv.textContent = msg.userInitial || '?';
            msgDiv.appendChild(avatarDiv);
        }
        
        const bodyDiv = document.createElement('div');
        bodyDiv.className = 'message-body';
        
        // Sender Name (if not joined)
        if (!msg.joined) {
            const fromNameDiv = document.createElement('div');
            fromNameDiv.className = `from-name ${msg.userPicColorClass || 'userpic1'}`;
            fromNameDiv.textContent = msg.fromName || '未知用户';
            bodyDiv.appendChild(fromNameDiv);
        }
        
        // Reply Block
        if(msg.replyToMessageId) {
             const replyDiv = document.createElement('div');
             replyDiv.className = 'reply-to';
             const replyLink = document.createElement('a');
             replyLink.href = `#message${msg.replyToMessageId}`;
             replyLink.innerHTML = `↪️ 回复消息 #${msg.replyToMessageId}`;
             replyDiv.appendChild(replyLink);
             bodyDiv.appendChild(replyDiv);
        }

        // Forwarded Message
        if(msg.forwardedMessage) {
            const fwdDiv = createForwardedMessageElement(msg.forwardedMessage);
            bodyDiv.appendChild(fwdDiv);
        }

        // Message Text
        if (msg.text) {
            const textDiv = document.createElement('div');
            textDiv.className = 'message-text';
            textDiv.innerHTML = msg.text; // Use innerHTML to keep formatting like <br> and <a>
            bodyDiv.appendChild(textDiv);
        }

        // Image
        if (msg.thumbnailUrl) {
            const imgLink = document.createElement('a');
            imgLink.href = msg.imageUrl;
            imgLink.target = '_blank';
            
            const thumbImg = document.createElement('img');
            thumbImg.src = msg.thumbnailUrl;
            thumbImg.className = 'media-thumbnail';
            
            imgLink.appendChild(thumbImg);
            bodyDiv.appendChild(imgLink);
        }

        // Reactions
        if (msg.reactions && msg.reactions.length > 0) {
            const reactionsDiv = document.createElement('div');
            reactionsDiv.className = 'reactions';
            msg.reactions.forEach(r => {
                const reactionSpan = document.createElement('span');
                // Check if emoji is not null or empty
                if (r.emoji) {
                    reactionSpan.className = 'reaction';
                    reactionSpan.textContent = r.emoji;
                    if(r.fromName) {
                        reactionSpan.title = `来自 ${r.fromName}`;
                    }
                    reactionsDiv.appendChild(reactionSpan);
                }
            });
            bodyDiv.appendChild(reactionsDiv);
        }

        // Timestamp
        const detailsDiv = document.createElement('span');
        detailsDiv.className = 'message-details';
        detailsDiv.textContent = msg.time || '';
        detailsDiv.title = msg.timestamp || '';
        bodyDiv.appendChild(detailsDiv);

        msgDiv.appendChild(bodyDiv);
        return msgDiv;
    }

    function createForwardedMessageElement(fwdMsg) {
        const fwdDiv = document.createElement('div');
        fwdDiv.className = 'forwarded-message';
        
        const fwdHeader = document.createElement('div');
        fwdHeader.className = 'forwarded-header';
        fwdHeader.textContent = `转发自 ${fwdMsg.fromName || '未知'}`;
        fwdDiv.appendChild(fwdHeader);
        
        if(fwdMsg.text) {
            const fwdText = document.createElement('div');
            fwdText.className = 'forwarded-text';
            fwdText.innerHTML = fwdMsg.text; // Use innerHTML to render links/formatting
            fwdDiv.appendChild(fwdText);
        }

        if(fwdMsg.thumbnailUrl) {
            const fwdImg = document.createElement('img');
            fwdImg.className = 'media-thumbnail';
            fwdImg.src = fwdMsg.thumbnailUrl;
            fwdDiv.appendChild(fwdImg);
        }
        return fwdDiv;
    }
});