/* ---  modernen.css --- A Modern UI Theme for the Chat Parser --- */

/* 1. CSS Variables for Theming (Light & Dark Mode) */
:root {
    --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;

    /* Light Theme */
    --bg-page: #f0f2f5;
    --bg-app: #ffffff;
    --bg-chat: #f0f2f5;
    --bg-bubble: #ffffff;
    --bg-bubble-reply: #f5f5f5;
    --bg-bubble-forward: #f2f8f2;
    --bg-service: #e9ebee;
    --bg-input: #f0f2f5;
    --bg-hover: #e9ebee;

    --text-primary: #050505;
    --text-secondary: #65676b;
    --text-tertiary: #8a8d91;
    --text-on-accent: #ffffff;

    --border-primary: #ced0d4;
    --border-secondary: #e0e0e0;

    --accent-primary: #3b82f6; /* A modern blue */
    --accent-primary-hover: #2563eb;
    --accent-forward: #16a34a; /* Green */
    --accent-error: #ef4444;
    --accent-error-bg: #fee2e2;

    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

html[data-theme='dark'] {
    --bg-page: #121212;
    --bg-app: #1e1e1e;
    --bg-chat: #121212;
    --bg-bubble: #2a2a2a;
    --bg-bubble-reply: #333333;
    --bg-bubble-forward: #1c3325;
    --bg-service: #2a2a2a;
    --bg-input: #333333;
    --bg-hover: #3a3a3a;

    --text-primary: #e4e6eb;
    --text-secondary: #b0b3b8;
    --text-tertiary: #8a8d91;

    --border-primary: #3e4042;
    --border-secondary: #2c2c2c;

    --accent-error: #fca5a5;
    --accent-error-bg: #451b1b;
}


/* 2. Global Resets and Base Styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: var(--font-sans);
    background-color: var(--bg-page);
    color: var(--text-primary);
    min-height: 100vh;
    display: grid;
    place-items: center;
    padding: 1rem;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* 3. Main Layout & App Container */
.page-wrapper {
    width: 100%;
    height: 100%;
}

.app-container {
    width: 100%;
    max-width: 840px;
    height: calc(100vh - 2rem);
    max-height: 900px;
    background-color: var(--bg-app);
    border-radius: 16px;
    box-shadow: var(--shadow-md);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: background-color 0.3s ease;
}

/* 4. App Header & Upload Form */
.app-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-secondary);
    flex-shrink: 0;
    transition: border-color 0.3s ease;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

h1 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.title-group p {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.theme-toggle-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    background-color: var(--bg-input);
    cursor: pointer;
    display: grid;
    place-items: center;
    transition: background-color 0.2s ease, transform 0.2s ease;
}

.theme-toggle-btn:hover {
    background-color: var(--bg-hover);
    transform: scale(1.1);
}

.theme-toggle-btn svg {
    width: 20px;
    height: 20px;
    fill: var(--text-primary);
}

.upload-form {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

#file-input {
    display: none;
}

.file-label {
    padding: 0.6rem 1.2rem;
    background-color: var(--bg-input);
    color: var(--text-primary);
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s ease;
}

.file-label:hover {
    background-color: var(--bg-hover);
}

.file-name {
    flex-grow: 1;
    font-size: 0.9rem;
    color: var(--text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.submit-btn {
    padding: 0.6rem 1.5rem;
    background-color: var(--accent-primary);
    color: var(--text-on-accent);
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.submit-btn:hover {
    background-color: var(--accent-primary-hover);
}

/* 5. Loading Indicator & Chat Header */
.loading-indicator {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-top: 1rem;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.spinner {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-primary);
    border-top-color: var(--accent-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.chat-header-bar {
    padding: 0.75rem 2rem;
    background-color: var(--bg-app);
    border-bottom: 1px solid var(--border-secondary);
    flex-shrink: 0;
}

#group-name-display {
    font-size: 1.1rem;
    font-weight: 500;
    text-align: center;
    color: var(--text-secondary);
}


/* 6. Chat Container & Messages */
.chat-container {
    flex-grow: 1;
    overflow-y: auto;
    padding: 1.5rem;
    background-color: var(--bg-chat);
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    transition: background-color 0.3s ease;
}

/* Custom Scrollbar */
.chat-container::-webkit-scrollbar {
    width: 8px;
}
.chat-container::-webkit-scrollbar-track {
    background: transparent;
}
.chat-container::-webkit-scrollbar-thumb {
    background-color: var(--border-primary);
    border-radius: 10px;
}
.chat-container::-webkit-scrollbar-thumb:hover {
    background-color: var(--text-tertiary);
}

.chat-placeholder {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    color: var(--text-tertiary);
    text-align: center;
}
.chat-placeholder span {
    font-size: 3rem;
    margin-bottom: 1rem;
}
.chat-placeholder p {
    font-size: 1.1rem;
    font-weight: 500;
}

/* 7. Message Bubbles */
.message {
    display: flex;
    gap: 0.75rem;
    align-items: flex-start;
    max-width: 85%;
}

.message.joined {
    padding-left: calc(40px + 0.75rem); /* avatar width + gap */
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    color: #fff;
    display: grid;
    place-items: center;
    font-weight: 700;
    flex-shrink: 0;
    margin-top: 5px; /* Align better with text */
}

/* User Pic Colors from Telegram Export */
.userpic1 { background-color: #9a6fff; }
.userpic2 { background-color: #55cde2; }
.userpic3 { background-color: #ffa152; }
.userpic4 { background-color: #ff6a6a; }
.userpic5 { background-color: #58b04f; }
.userpic6 { background-color: #528ecc; }
.userpic7 { background-color: #f584c3; }
.userpic8 { background-color: #f7b748; }

.message-body {
    background-color: var(--bg-bubble);
    padding: 0.6rem 1rem;
    border-radius: 18px;
    border-top-left-radius: 6px;
    box-shadow: var(--shadow-sm);
    position: relative;
    min-width: 80px;
}

.message-body .from-name {
    font-weight: 700;
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

/* Colors for sender names based on avatar color */
.from-name.userpic1 { color: #9a6fff; }
.from-name.userpic2 { color: #55cde2; }
.from-name.userpic3 { color: #ffa152; }
.from-name.userpic4 { color: #ff6a6a; }
.from-name.userpic5 { color: #58b04f; }
.from-name.userpic6 { color: #528ecc; }
.from-name.userpic7 { color: #f584c3; }
.from-name.userpic8 { color: #f7b748; }

.message-text {
    font-size: 1rem;
    line-height: 1.5;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.message-text a {
    color: var(--accent-primary);
    text-decoration: none;
    font-weight: 500;
}
.message-text a:hover {
    text-decoration: underline;
}

.message-details {
    font-size: 0.75rem;
    color: var(--text-tertiary);
    position: absolute;
    bottom: 6px;
    right: 12px;
    user-select: none;
}

/* Make room for timestamp */
.message-body {
    padding-bottom: 1.5rem;
}


/* 8. Special Message Types */
.service-message {
    text-align: center;
    margin: 1rem 0;
}

.service-message .details {
    display: inline-block;
    background-color: var(--bg-service);
    color: var(--text-secondary);
    padding: 0.4rem 0.8rem;
    border-radius: 16px;
    font-size: 0.8rem;
    font-weight: 500;
}
.service-message .details.error {
    background-color: var(--accent-error-bg);
    color: var(--accent-error);
}

.reply-to {
    background-color: var(--bg-bubble-reply);
    border-left: 3px solid var(--accent-primary);
    padding: 0.5rem 0.75rem;
    margin: -0.25rem 0 0.5rem;
    border-radius: 6px;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.forwarded-message {
    border-left: 3px solid var(--accent-forward);
    padding: 0.5rem 0.75rem;
    margin: -0.25rem 0 0.5rem;
    border-radius: 6px;
    background-color: var(--bg-bubble-forward);
    color: var(--text-secondary);
}

.forwarded-header {
    font-weight: 700;
    color: var(--accent-forward);
    font-size: 0.85em;
}

.forwarded-text {
    font-size: 0.95em;
    margin-top: 0.25rem;
}

.media-thumbnail {
    max-width: 100%;
    max-height: 350px;
    border-radius: 12px;
    cursor: pointer;
    margin-top: 0.5rem;
    display: block;
}

.reactions {
    position: absolute;
    bottom: -12px;
    left: 10px;
    display: flex;
    gap: 4px;
}

.reaction {
    background-color: var(--bg-app);
    border: 1px solid var(--border-secondary);
    border-radius: 12px;
    padding: 2px 8px;
    font-size: 0.9em;
    user-select: none;
    box-shadow: var(--shadow-sm);
    cursor: default;
}

}

.chat-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    color: #777;
    font-size: 1.2em;
}

/* Group Header */
.group-header {
    text-align: center;
    font-weight: bold;
    color: #075e54;
    background-color: #fff;
    padding: 10px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* Message Bubbles */
.message {
    display: flex;
    margin-bottom: 5px;
    align-items: flex-end;
}

.message.joined {
    margin-top: -3px; /* Continuous messages are closer */
    padding-left: 50px; /* Align with previous message */
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    margin-right: 10px;
    flex-shrink: 0;
}

/* User Pic Colors from Telegram Export */
.userpic1 { background-color: #9a6fff; }
.userpic2 { background-color: #55cde2; }
.userpic3 { background-color: #ffa152; }
.userpic4 { background-color: #ff6a6a; }
.userpic5 { background-color: #58b04f; }
.userpic6 { background-color: #528ecc; }
.userpic7 { background-color: #f584c3; }
.userpic8 { background-color: #f7b748; }


.message-body {
    max-width: 70%;
    background-color: #fff;
    padding: 8px 12px;
    border-radius: 12px;
    position: relative;
    box-shadow: 0 1px 1px rgba(0,0,0,0.05);
}

.message-body .from-name {
    font-weight: bold;
    margin-bottom: 4px;
    font-size: 0.9em;
}

/* Colors for sender names based on avatar color */
.from-name.userpic1 { color: #9a6fff; }
.from-name.userpic2 { color: #55cde2; }
.from-name.userpic3 { color: #ffa152; }
.from-name.userpic4 { color: #ff6a6a; }
.from-name.userpic5 { color: #58b04f; }
.from-name.userpic6 { color: #528ecc; }
.from-name.userpic7 { color: #f584c3; }
.from-name.userpic8 { color: #f7b748; }

.message-text {
    font-size: 1em;
    line-height: 1.4;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.message-text a {
    color: #1a73e8;
    text-decoration: none;
}
.message-text a:hover {
    text-decoration: underline;
}

.message-details {
    font-size: 0.75em;
    color: #888;
    float: right;
    margin-left: 10px;
    margin-top: 5px;
    user-select: none;
}


/* Service Messages (Dates, Pinned, etc.) */
.service-message {
    text-align: center;
    margin: 15px 0;
}

.service-message .details {
    display: inline-block;
    background-color: rgba(0, 0, 0, 0.1);
    color: #555;
    padding: 5px 10px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: 500;
}

/* Reply Block */
.reply-to {
    background-color: #f0f0f0;
    border-left: 3px solid #1a73e8;
    padding: 6px 8px;
    margin-bottom: 5px;
    border-radius: 4px;
    font-size: 0.9em;
}

/* Forwarded Message Block */
.forwarded-message {
    border-left: 3px solid #58b04f;
    padding: 6px 8px;
    margin: 5px 0;
    border-radius: 4px;
    background-color: #f2f8f2;
}

.forwarded-header {
    font-weight: bold;
    color: #58b04f;
    font-size: 0.85em;
}

.forwarded-text {
    font-size: 0.95em;
    margin-top: 4px;
}

/* Media (Images) */
.media-thumbnail {
    max-width: 100%;
    max-height: 300px;
    border-radius: 8px;
    cursor: pointer;
    margin-top: 5px;
}

/* Reactions */
.reactions {
    margin-top: 5px;
}
.reaction {
    display: inline-block;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 0.9em;
    margin-right: 4px;
    user-select: none;
}