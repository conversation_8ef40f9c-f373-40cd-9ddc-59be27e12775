<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Telegram 聊天记录解析器</title>
    <!-- 引入现代字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" th:href="@{/css/style.css}" id="theme-stylesheet">
</head>
<body>

<div class="page-wrapper">
    <div class="app-container">
        <header class="app-header">
            <div class="header-content">
                <div class="title-group">
                    <h1>TG-Parser</h1>
                    <p>现代化的 Telegram 聊天记录解析器</p>
                </div>
                <button id="theme-toggle-btn" class="theme-toggle-btn" aria-label="切换深色/浅色主题">
                    <!-- SVG icon for theme toggle -->
                </button>
            </div>
            <form id="upload-form" class="upload-form">
                <label for="file-input" class="file-label">
                    <span>选择文件</span>
                </label>
                <input type="file" id="file-input" name="file" accept=".html,.htm" required>
                <span id="file-name" class="file-name">未选择文件</span>
                <button type="submit" class="submit-btn">解析</button>
            </form>
            <div id="loading-indicator" class="loading-indicator" style="display:none;">
                <div class="spinner"></div>
                正在解析，请稍候...
            </div>
        </header>
        
        <div id="chat-header-bar" class="chat-header-bar" style="display: none;">
            <h2 id="group-name-display"></h2>
        </div>
        <main id="chat-container" class="chat-container">
            <div class="chat-placeholder">
                <span>👆</span>
                <p>请先上传聊天记录文件</p>
            </div>
        </main>
    </div>
</div>

<script th:src="@{/js/script.js}"></script>

</body>
</html>