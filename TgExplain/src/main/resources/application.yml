spring:
  datasource:
    url: **************************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: true
    database-platform: org.hibernate.dialect.MySQLDialect

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB        # 单个文件最大10MB
      max-request-size: 50MB     # 总请求大小最大50MB
      enabled: true

server:
  port: 8080

logging:
  level:
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE