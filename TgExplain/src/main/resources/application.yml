spring:
  datasource:
    url: **************************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: true
    database-platform: org.hibernate.dialect.MySQLDialect

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB        # 单个文件最大10MB
      max-request-size: 50MB     # 总请求大小最大50MB
      enabled: true

# 应用程序配置
app:
  # 文件存储配置
  file-storage:
    root-path: uploads                    # 文件存储根目录
    create-date-directories: true         # 是否按日期创建子目录
    create-group-directories: true        # 是否按群组创建子目录
    file-prefix: "tg_chat_"              # 文件名前缀
    include-timestamp: true               # 是否在文件名中包含时间戳
    preserve-original-name: true          # 是否保留原始文件名
    max-file-name-length: 200            # 文件名最大长度

server:
  port: 8080

logging:
  level:
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE