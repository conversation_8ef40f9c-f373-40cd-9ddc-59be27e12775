package xiaowu.tgexplain;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
public class ChatParser {
    // 使用一个简单的内部类来模拟最终的数据库实体
    public static class ChatMessage {
        public String sourceMessageId;
        String groupName;
        public String sender;
        public String content;
        public String sentTime;
        public String messageType;
        public boolean isJoined;
        public String replyToSourceId;

        @Override
        public String toString() {
            return "ChatMessage {\n" +
                    "  源消息ID='" + sourceMessageId + "',\n" +
                    "  群组名称='" + groupName + "',\n" +
                    "  发送者='" + sender + "',\n" +
                    "  发送时间='" + sentTime + "',\n" +
                    "  消息类型='" + messageType + "',\n" +
                    "  是否连续消息=" + isJoined + ",\n" +
                    "  回复的源消息ID='" + (replyToSourceId == null ? "无" : replyToSourceId) + "',\n" +
                    "  聊天内容='" + content.replace("\n", "\\n") + "'\n" +
                    '}';
        }
    }
    

    public static void main(String[] args) {
        // 使用绝对路径以确保文件能被找到
        File input = new File("/Users/<USER>/Desktop/上海期货交易所2TG(1)/chats/chat_01/messages.html");
        try {
            List<ChatMessage> messages = parse(input);
            System.out.println("==================================================");
            System.out.println("解析完成！共找到 " + messages.size() + " 条消息。");
            System.out.println("==================================================");
            // 打印一些消息作为示例，特别是包含回复的消息
            for (int i = 0; i < messages.size(); i++) {
                 // 找到一条回复消息并打印它和它前后的几条消息作为上下文
                if (messages.get(i).replyToSourceId != null) {
                    System.out.println("\n... (找到一条回复消息，展示其上下文) ...");
                    for(int j = Math.max(0, i - 2); j < Math.min(messages.size(), i + 3); j++) {
                        System.out.println("\n----------- 消息 " + (j + 1) + " -----------");
                        System.out.println(messages.get(j));
                    }
                    return; // 找到一个例子后就退出
                }
            }
            // 如果没有找到回复消息，就打印前15条
            System.out.println("\n... (未找到回复消息，打印前15条) ...");
            for (int i = 0; i < Math.min(messages.size(), 15); i++) {
                System.out.println("\n----------- 消息 " + (i + 1) + " -----------");
                System.out.println(messages.get(i));
            }

        } catch (IOException e) {
            System.err.println("处理文件时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
    public static List<ChatMessage> parse(File htmlFile) throws IOException {
        Document doc = Jsoup.parse(htmlFile, StandardCharsets.UTF_8.name(), "");
        List<ChatMessage> chatMessages = new ArrayList<>();

        String groupName = doc.select(".page_header .text").text().trim();
        Elements messages = doc.select(".history .message");
        String lastSender = "";
        String currentDate = "";
        // 最终修正正则表达式
        Pattern replyPattern = Pattern.compile("GoToMessage\\((-?\\d+)\\)");

        for (Element msgElement : messages) {
            ChatMessage msg = new ChatMessage();
            msg.groupName = groupName;
            msg.sourceMessageId = msgElement.id();

            // 1. 处理系统消息 (包括日期分隔)
            if (msgElement.hasClass("service")) {
                String serviceText = msgElement.select(".body.details").text().trim();
                if (isDateSeparator(serviceText)) {
                    currentDate = serviceText;
                    continue;
                }
                msg.messageType = "系统消息";
                msg.sender = "系统";
                msg.content = serviceText;
                msg.sentTime = currentDate;
                msg.isJoined = false;
                chatMessages.add(msg);
                continue;
            }

            // 2. 处理普通消息
            msg.isJoined = msgElement.hasClass("joined");

            Element fromNameElement = msgElement.select(".from_name").first();
            if (fromNameElement != null) {
                msg.sender = fromNameElement.text().trim();
                lastSender = msg.sender;
            } else {
                msg.sender = lastSender;
            }

            Element dateElement = msgElement.select(".pull_right.date.details").first();
            if (dateElement != null) {
                msg.sentTime = dateElement.attr("title").trim();
            }

            // 使用修正后的逻辑提取回复ID
            Element replyElement = msgElement.select(".reply_to a").first();
            if (replyElement != null) {
                Matcher matcher = replyPattern.matcher(replyElement.attr("onclick"));
                if (matcher.find()) {
                    msg.replyToSourceId = "message" + matcher.group(1);
                }
            }

            Element textElement = msgElement.select(".text").first();
            String textContent = "";
            if (textElement != null) {
                textContent = textElement.html().replaceAll("(?i)<br\\s*/?>", "\n");
                textContent = Jsoup.parse(textContent).text().trim();
            }
            
            Element mediaElement = msgElement.select(".media_wrap").first();
            if (mediaElement != null) {
                if (mediaElement.select(".photo_wrap").first() != null) {
                    msg.messageType = "图片";
                } else if (mediaElement.select("a[href*='sticker.webm']").first() != null) {
                    msg.messageType = "贴纸/视频";
                } else if (mediaElement.select(".video_file").first() != null) {
                    msg.messageType = "视频";
                } else {
                    msg.messageType = "媒体";
                }
            } else {
                msg.messageType = "文本";
            }
            msg.content = textContent;
            chatMessages.add(msg);
        }
        return chatMessages;
    }

    private static boolean isDateSeparator(String text) {
        return text.matches("\\d{1,2} \\w+ \\d{4}");
    }
}