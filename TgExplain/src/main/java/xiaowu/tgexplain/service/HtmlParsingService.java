package xiaowu.tgexplain.service;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import lombok.RequiredArgsConstructor;
import org.jsoup.nodes.Element;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import xiaowu.tgexplain.model.ChatMessage;
import xiaowu.tgexplain.model.Message;
import xiaowu.tgexplain.model.Reaction;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
public class HtmlParsingService {

    private final DatabaseStorageService storageService;

    public ChatMessage parseChatMessages(MultipartFile htmlFile) throws IOException{
        Document doc = Jsoup.parse(htmlFile.getInputStream(), "UTF-8", "");
        ChatMessage chatMessage = new ChatMessage();
        List<Message> messages = new ArrayList<>();

        // 1. 解析群组名称
        Element groupNameElement = doc.selectFirst(".page_header .content .text.bold");
        if (groupNameElement != null) {
            chatMessage.setGroupName(groupNameElement.text().trim());
        }

        Message lastMessageFromUser = null; // 用于处理连续消息（joined）

        // 2. 遍历所有消息元素
        for (Element msgElement : doc.select(".message")) {
            Message message = new Message();
            message.setId(msgElement.id());

            // 3. 判断并处理不同类型的消息
            if (msgElement.hasClass("service")) {
                // --- 服务类消息 (日期, 邀请, 置顶等) ---
                message.setType("service");
                Element details = msgElement.selectFirst(".body.details");
                if (details != null) {
                    Element pinnedLink = details.selectFirst("a");
                    if (pinnedLink != null) {
                        // 置顶消息
                        message.setPinnedMessageText(details.text());
                        String onclickAttr = pinnedLink.attr("onclick"); // "return GoToMessage(9249)"
                        message.setPinnedMessageId(onclickAttr.replaceAll("[^0-9]", ""));
                    } else {
                        // 日期分隔符或其他服务信息
                        message.setDateDetails(details.text().trim());
                    }
                }
                lastMessageFromUser = null; // 服务消息会中断连续对话

            } else if (msgElement.hasClass("default")) {
                // --- 普通聊天消息 ---
                message.setType("default");
                Element body = msgElement.selectFirst(".body");
                if (body == null) continue; // 如果消息没有 body，则跳过

                // 4. 处理连续消息 (joined)
                if (msgElement.hasClass("joined")) {
                    message.setJoined(true);
                    // 如果是连续消息, 从上一条消息中继承用户信息
                    if (lastMessageFromUser != null) {
                        message.setFromName(lastMessageFromUser.getFromName());
                        message.setUserPicColorClass(lastMessageFromUser.getUserPicColorClass());
                        message.setUserInitial(lastMessageFromUser.getUserInitial());
                    }
                } else {
                    message.setJoined(false);
                    // 解析用户信息 (头像, 颜色, 昵称)
                    Element fromNameElement = body.selectFirst(".from_name");
                    if (fromNameElement != null) {
                        message.setFromName(fromNameElement.ownText().trim());
                    }

                    Element userPicWrap = msgElement.selectFirst(".pull_left.userpic_wrap .userpic");
                    if (userPicWrap != null) {
                        for (String className : userPicWrap.classNames()) {
                            if (className.startsWith("userpic")) {
                                message.setUserPicColorClass(className);
                                break;
                            }
                        }
                        Element initialDiv = userPicWrap.selectFirst(".initials");
                        if (initialDiv != null) {
                            message.setUserInitial(initialDiv.text().trim());
                        }
                    }
                    lastMessageFromUser = message; // 更新最后一位发言人
                }

                // 5. 解析通用消息内容 (时间, 文本, 回复等)
                // 时间和时间戳
                Element dateElement = body.selectFirst(".pull_right.date.details");
                if (dateElement != null) {
                    message.setTime(dateElement.text().trim());
                    message.setTimestamp(dateElement.attr("title"));
                }

                // 回复
                Element replyToElement = body.selectFirst(".reply_to a");
                if (replyToElement != null) {
                    String onclick = replyToElement.attr("onclick");
                    message.setReplyToMessageId(onclick.replaceAll("[^0-9]", ""));
                }

                // 消息文本 (使用 .html() 保留链接和换行)
                Element textElement = body.selectFirst(".text");
                StringBuilder textContent = new StringBuilder();
                if (textElement != null) {
                    textContent.append(textElement.html());
                }

                // 转发消息
                Element forwardedBody = body.selectFirst(".forwarded.body");
                if (forwardedBody != null) {
                    message.setForwardedMessage(parseForwardedMessage(forwardedBody));
                }

                // 多媒体内容
                Element mediaWrap = body.selectFirst(".media_wrap");
                if (mediaWrap != null) {
                    // 图片
                    Element photoWrap = mediaWrap.selectFirst("a.photo_wrap");
                    if (photoWrap != null) {
                        message.setImageUrl(photoWrap.attr("href"));
                        Element thumbImg = photoWrap.selectFirst("img.photo");
                        if (thumbImg != null) {
                            message.setThumbnailUrl(thumbImg.attr("src"));
                        }
                    }
                    // 未下载的贴纸或其他媒体
                    Element mediaPlaceholder = mediaWrap.selectFirst(".media.media_photo, .media.media_video");
                    if (mediaPlaceholder != null) {
                         Element title = mediaPlaceholder.selectFirst(".title");
                         Element desc = mediaPlaceholder.selectFirst(".description");
                         if (title != null && desc != null) {
                             if (!textContent.isEmpty()) textContent.append("<br>");
                             textContent.append("<i>[媒体文件: ").append(title.text()).append(" - ").append(desc.text()).append("]</i>");
                         }
                    }
                }
                message.setText(textContent.toString());

                // 消息回应 (Reactions)
                Element reactionsWrap = body.selectFirst(".reactions");
                if(reactionsWrap != null) {
                    List<Reaction> reactionList = new ArrayList<>();
                    for(Element reactionElem : reactionsWrap.select(".reaction")) {
                        Reaction reaction = new Reaction();
                        Element emojiElem = reactionElem.selectFirst(".emoji");
                        Element userPicElem = reactionElem.selectFirst(".userpic .initials");
                        if (emojiElem != null) {
                            reaction.setEmoji(emojiElem.text());
                        }
                        if (userPicElem != null) {
                            reaction.setFromName(userPicElem.attr("title"));
                            reaction.setUserInitial(userPicElem.text());
                        }
                        reactionList.add(reaction);
                    }
                    message.setReactions(reactionList);
                }
            }

            messages.add(message);
        }

        chatMessage.setMessages(messages);
        return chatMessage;
    }

    // 用于解析转发消息块的辅助方法
    private Message parseForwardedMessage(Element forwardedBody) {
        Message fwdMessage = new Message();
        fwdMessage.setType("forwarded");

        // 解析转发来源
        Element fromNameElement = forwardedBody.selectFirst(".from_name");
        if (fromNameElement != null) {
            fwdMessage.setFromName(fromNameElement.ownText().trim());
            Element dateSpan = fromNameElement.selectFirst("span.date");
            if (dateSpan != null) {
                fwdMessage.setTime(dateSpan.text().trim());
                fwdMessage.setTimestamp(dateSpan.attr("title"));
            }
        }

        // 解析转发内容的文本
        Element textElement = forwardedBody.selectFirst(".text");
        if (textElement != null) {
            fwdMessage.setText(textElement.html());
        }

        // 解析转发内容中的图片
        Element photoWrap = forwardedBody.selectFirst(".media_wrap a.photo_wrap");
        if (photoWrap != null) {
            fwdMessage.setImageUrl(photoWrap.attr("href"));
            Element thumbImg = photoWrap.selectFirst("img.photo");
            if (thumbImg != null) {
                fwdMessage.setThumbnailUrl(thumbImg.attr("src"));
            }
        }
        return fwdMessage;
    }
}
