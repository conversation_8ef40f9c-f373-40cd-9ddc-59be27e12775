package xiaowu.tgexplain.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import xiaowu.tgexplain.config.FileStorageConfig;
import xiaowu.tgexplain.exception.FileStorageException;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * 文件存储服务类
 * 
 * 负责HTML文件的本地存储管理，包括文件保存、路径生成、目录创建等功能。
 * 严格遵循《阿里巴巴Java开发手册》的异常处理和资源管理规范。
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileStorageService {
    
    private final FileStorageConfig fileStorageConfig;
    
    // 日期格式化器，用于创建日期目录
    private static final DateTimeFormatter DATE_DIR_FORMATTER = DateTimeFormatter.ofPattern("yyyy/MM/dd");
    private static final DateTimeFormatter TIMESTAMP_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");
    
    /**
     * 保存上传的HTML文件到本地存储
     * 
     * @param file 上传的文件
     * @param groupName 群组名称，用于创建目录结构
     * @return 保存后的文件相对路径
     * @throws FileStorageException 文件存储异常
     */
    public String saveHtmlFile(MultipartFile file, String groupName) throws FileStorageException {
        validateFile(file);
        
        try {
            // 1. 生成存储路径
            Path targetPath = generateStoragePath(file, groupName);
            
            // 2. 确保目录存在
            createDirectoriesIfNotExists(targetPath.getParent());
            
            // 3. 保存文件
            saveFileToPath(file, targetPath);
            
            // 4. 返回相对路径
            String relativePath = getRelativePath(targetPath);
            
            log.info("文件保存成功: {} -> {}", file.getOriginalFilename(), relativePath);
            return relativePath;
            
        } catch (IOException e) {
            String errorMsg = String.format("保存文件失败: %s", file.getOriginalFilename());
            log.error(errorMsg, e);
            throw new FileStorageException(errorMsg, e);
        }
    }
    
    /**
     * 验证上传的文件
     * 
     * @param file 待验证的文件
     * @throws FileStorageException 验证失败时抛出异常
     */
    private void validateFile(MultipartFile file) throws FileStorageException {
        if (file == null || file.isEmpty()) {
            throw new FileStorageException("上传文件不能为空");
        }
        
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            throw new FileStorageException("文件名不能为空");
        }
        
        // 验证文件扩展名
        String lowerCaseFilename = originalFilename.toLowerCase();
        if (!lowerCaseFilename.endsWith(".html") && !lowerCaseFilename.endsWith(".htm")) {
            throw new FileStorageException("只支持HTML文件格式 (.html, .htm)");
        }
    }
    
    /**
     * 生成文件存储路径
     * 
     * @param file 上传的文件
     * @param groupName 群组名称
     * @return 完整的存储路径
     */
    private Path generateStoragePath(MultipartFile file, String groupName) {
        Path basePath = fileStorageConfig.getStorageRootPath();
        
        // 1. 添加日期目录
        if (fileStorageConfig.isCreateDateDirectories()) {
            String dateDir = LocalDateTime.now().format(DATE_DIR_FORMATTER);
            basePath = basePath.resolve(dateDir);
        }
        
        // 2. 添加群组目录
        if (fileStorageConfig.isCreateGroupDirectories() && groupName != null) {
            String sanitizedGroupName = fileStorageConfig.sanitizeGroupName(groupName);
            basePath = basePath.resolve(sanitizedGroupName);
        }
        
        // 3. 生成文件名
        String fileName = generateFileName(file);
        
        return basePath.resolve(fileName);
    }
    
    /**
     * 生成唯一的文件名
     * 
     * @param file 上传的文件
     * @return 生成的文件名
     */
    private String generateFileName(MultipartFile file) {
        StringBuilder fileName = new StringBuilder();
        
        // 1. 添加前缀
        if (fileStorageConfig.getFilePrefix() != null && !fileStorageConfig.getFilePrefix().isEmpty()) {
            fileName.append(fileStorageConfig.getFilePrefix());
        }
        
        // 2. 添加时间戳
        if (fileStorageConfig.isIncludeTimestamp()) {
            fileName.append(LocalDateTime.now().format(TIMESTAMP_FORMATTER));
            fileName.append("_");
        }
        
        // 3. 添加UUID确保唯一性
        fileName.append(UUID.randomUUID().toString().replace("-", "").substring(0, 8));
        
        // 4. 添加原始文件名
        if (fileStorageConfig.isPreserveOriginalName()) {
            String originalName = file.getOriginalFilename();
            if (originalName != null) {
                fileName.append("_");
                fileName.append(fileStorageConfig.sanitizeFileName(originalName));
            }
        } else {
            // 如果不保留原始文件名，至少要保留扩展名
            String originalName = file.getOriginalFilename();
            if (originalName != null) {
                int lastDotIndex = originalName.lastIndexOf('.');
                if (lastDotIndex > 0) {
                    fileName.append(originalName.substring(lastDotIndex));
                } else {
                    fileName.append(".html");  // 默认扩展名
                }
            }
        }
        
        return fileName.toString();
    }
    
    /**
     * 创建目录（如果不存在）
     * 
     * @param dirPath 目录路径
     * @throws IOException 创建目录失败时抛出异常
     */
    private void createDirectoriesIfNotExists(Path dirPath) throws IOException {
        if (dirPath != null && !Files.exists(dirPath)) {
            Files.createDirectories(dirPath);
            log.debug("创建目录: {}", dirPath);
        }
    }
    
    /**
     * 将文件保存到指定路径
     * 
     * @param file 上传的文件
     * @param targetPath 目标路径
     * @throws IOException 保存失败时抛出异常
     */
    private void saveFileToPath(MultipartFile file, Path targetPath) throws IOException {
        try (InputStream inputStream = file.getInputStream()) {
            Files.copy(inputStream, targetPath, StandardCopyOption.REPLACE_EXISTING);
        }
    }
    
    /**
     * 获取相对于存储根目录的相对路径
     * 
     * @param absolutePath 绝对路径
     * @return 相对路径字符串
     */
    private String getRelativePath(Path absolutePath) {
        Path rootPath = fileStorageConfig.getStorageRootPath();
        try {
            return rootPath.relativize(absolutePath).toString().replace('\\', '/');
        } catch (IllegalArgumentException e) {
            // 如果无法计算相对路径，返回绝对路径
            log.warn("无法计算相对路径，返回绝对路径: {}", absolutePath);
            return absolutePath.toString().replace('\\', '/');
        }
    }
    
    /**
     * 检查文件是否存在
     * 
     * @param relativePath 相对路径
     * @return 文件是否存在
     */
    public boolean fileExists(String relativePath) {
        if (relativePath == null || relativePath.trim().isEmpty()) {
            return false;
        }
        
        Path filePath = fileStorageConfig.getStorageRootPath().resolve(relativePath);
        return Files.exists(filePath) && Files.isRegularFile(filePath);
    }
    
    /**
     * 获取文件的绝对路径
     * 
     * @param relativePath 相对路径
     * @return 绝对路径
     */
    public Path getAbsolutePath(String relativePath) {
        return fileStorageConfig.getStorageRootPath().resolve(relativePath);
    }
    
    /**
     * 删除文件
     * 
     * @param relativePath 相对路径
     * @return 是否删除成功
     */
    public boolean deleteFile(String relativePath) {
        try {
            Path filePath = getAbsolutePath(relativePath);
            boolean deleted = Files.deleteIfExists(filePath);
            if (deleted) {
                log.info("文件删除成功: {}", relativePath);
            }
            return deleted;
        } catch (IOException e) {
            log.error("删除文件失败: {}", relativePath, e);
            return false;
        }
    }
}
