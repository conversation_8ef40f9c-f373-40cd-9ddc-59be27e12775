package xiaowu.tgexplain.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import xiaowu.tgexplain.entity.MessageEntity;
import xiaowu.tgexplain.exception.FileStorageException;
import xiaowu.tgexplain.repository.MessageRepository;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class DatabaseStorageService {

    private final MessageRepository messageRepository;
    private final FileStorageService fileStorageService;

    // 定义日期时间格式，以匹配HTML中的格式 (e.g., 21.07.2024 15:30:01)
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("dd.MM.yyyy HH:mm:ss");

    @Transactional
    public String processAndSaveHtml(MultipartFile file) throws IOException {
        String savedFilePath = null;

        try (InputStream inputStream = file.getInputStream()) {
            Document doc = Jsoup.parse(inputStream, "UTF-8", "");

            // 1. 获取群组名称 (通常在 class="text bold" 的 h2 标签中)
            String groupName = doc.select("div.page_header .text.bold").text();
            if (groupName.isEmpty()) {
                groupName = "未知群组";
            }

            // 2. 保存HTML文件到本地存储
            try {
                savedFilePath = fileStorageService.saveHtmlFile(file, groupName);
                log.info("HTML文件保存成功: {} -> {}", file.getOriginalFilename(), savedFilePath);
            } catch (FileStorageException e) {
                log.error("保存HTML文件失败: {}", file.getOriginalFilename(), e);
                throw new IOException("文件存储失败: " + e.getMessage(), e);
            }

            // 3. 选择所有消息块 (通常是 class="message default" 的 div)
            Elements messageBlocks = doc.select(".message.default");
            int processedCount = 0;

            for (Element messageBlock : messageBlocks) {
                MessageEntity message = new MessageEntity();
                message.setGroupName(groupName);

                // 设置HTML文件路径
                message.setHtmlFilePath(savedFilePath);

                // 4. 提取业务ID (在 id 属性中, e.g., "message123")
                String businessId = messageBlock.id();
                if (businessId.isEmpty() || messageRepository.existsByBusinessId(businessId)) {
                    continue; // 如果没有ID或已存在，则跳过
                }
                message.setBusinessId(businessId);

                // 5. 提取发送者 (在 class="from_name" 的 div 中)
                Element fromNameElement = messageBlock.select(".from_name").first();
                if (fromNameElement != null) {
                    message.setSenderName(fromNameElement.text().trim());
                }

                // 6. 提取消息时间 (在 class="date" 的 div 的 title 属性中)
                Element dateElement = messageBlock.select(".date").first();
                if (dateElement != null) {
                    String timestamp = dateElement.attr("title");
                    try {
                        message.setMessageTime(LocalDateTime.parse(timestamp, DATE_TIME_FORMATTER));
                    } catch (DateTimeParseException e) {
                        // 如果解析失败，可以设置一个默认时间或记录日志
                        log.warn("解析消息时间失败: {}, 使用当前时间", timestamp);
                        message.setMessageTime(LocalDateTime.now());
                    }
                }

                // 7. 检查是否为回复
                Element replyElement = messageBlock.select(".reply_to").first();
                if (replyElement != null) {
                    message.setReply(true);
                    // 提取被回复者
                    Element repliedUserElement = replyElement.select(".from_name").first();
                    if (repliedUserElement != null) {
                        message.setRepliedToUser(repliedUserElement.text().trim());
                    }
                    // 提取被回复的消息内容
                    Element repliedMessageElement = replyElement.select(".text").first();
                    if (repliedMessageElement != null) {
                        message.setRepliedMessageContent(repliedMessageElement.text().trim());
                    }
                } else {
                    message.setReply(false);
                }

                // 8. 提取消息文本 (在 class="text" 的 div 中)
                Element textElement = messageBlock.select(".text").first();
                if (textElement != null) {
                    message.setMessageContent(textElement.html().trim());
                }

                // 9. 提取图片 (在 class="photo_wrap" 的 a 标签的 href 属性中)
                Element photoElement = messageBlock.select(".photo_wrap a").first();
                if (photoElement != null) {
                    message.setImageUrl(photoElement.attr("href"));
                }

                messageRepository.save(message);
                processedCount++;
            }

            String result = String.format("文件处理成功，共解析 %d 条消息，文件保存至: %s",
                    processedCount, savedFilePath);
            log.info("HTML文件处理完成: {} - {}", file.getOriginalFilename(), result);
            return result;

        } catch (Exception e) {
            // 如果处理过程中出现异常，尝试清理已保存的文件
            if (savedFilePath != null) {
                try {
                    fileStorageService.deleteFile(savedFilePath);
                    log.info("已清理异常处理过程中保存的文件: {}", savedFilePath);
                } catch (Exception cleanupException) {
                    log.warn("清理文件失败: {}", savedFilePath, cleanupException);
                }
            }
            throw e;
        }
        }
    }

    @Transactional(readOnly = true)
    public List<String> getDistinctGroupNames() {
        return messageRepository.findDistinctGroupNames();
    }

    @Transactional(readOnly = true)
    public List<MessageEntity> getMessagesByGroupName(String groupName) {
        return messageRepository.findByGroupNameOrderByMessageTimeAsc(groupName);
    }
}
