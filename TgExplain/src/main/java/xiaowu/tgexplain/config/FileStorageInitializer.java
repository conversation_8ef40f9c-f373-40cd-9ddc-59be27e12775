package xiaowu.tgexplain.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

/**
 * 文件存储初始化器
 * 
 * 在应用启动时检查和创建必要的存储目录。
 * 遵循《阿里巴巴Java开发手册》的初始化规范。
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FileStorageInitializer implements CommandLineRunner {
    
    private final FileStorageConfig fileStorageConfig;
    
    @Override
    public void run(String... args) throws Exception {
        initializeStorageDirectories();
    }
    
    /**
     * 初始化存储目录
     */
    private void initializeStorageDirectories() {
        try {
            Path rootPath = fileStorageConfig.getStorageRootPath();
            
            // 创建根目录
            if (!Files.exists(rootPath)) {
                Files.createDirectories(rootPath);
                log.info("创建文件存储根目录: {}", rootPath);
            } else {
                log.info("文件存储根目录已存在: {}", rootPath);
            }
            
            // 检查目录权限
            if (!Files.isWritable(rootPath)) {
                log.error("文件存储根目录不可写: {}", rootPath);
                throw new IllegalStateException("文件存储根目录不可写: " + rootPath);
            }
            
            // 输出配置信息
            logStorageConfiguration();
            
        } catch (IOException e) {
            log.error("初始化文件存储目录失败", e);
            throw new IllegalStateException("初始化文件存储目录失败", e);
        }
    }
    
    /**
     * 输出存储配置信息
     */
    private void logStorageConfiguration() {
        log.info("=== 文件存储配置信息 ===");
        log.info("存储根目录: {}", fileStorageConfig.getStorageRootPath());
        log.info("相对路径: {}", fileStorageConfig.getRelativeStoragePath());
        log.info("按日期创建目录: {}", fileStorageConfig.isCreateDateDirectories());
        log.info("按群组创建目录: {}", fileStorageConfig.isCreateGroupDirectories());
        log.info("文件名前缀: {}", fileStorageConfig.getFilePrefix());
        log.info("包含时间戳: {}", fileStorageConfig.isIncludeTimestamp());
        log.info("保留原始文件名: {}", fileStorageConfig.isPreserveOriginalName());
        log.info("文件名最大长度: {}", fileStorageConfig.getMaxFileNameLength());
        log.info("========================");
    }
}
