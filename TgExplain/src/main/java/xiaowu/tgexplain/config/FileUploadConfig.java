package xiaowu.tgexplain.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import lombok.Data;

/**
 * 文件上传配置类
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "app.upload")
public class FileUploadConfig {
    
    /**
     * 单个文件最大大小（字节）
     */
    private long maxFileSize = 10 * 1024 * 1024; // 10MB
    
    /**
     * 最大文件数量
     */
    private int maxFileCount = 5;
    
    /**
     * 总请求最大大小（字节）
     */
    private long maxRequestSize = 50 * 1024 * 1024; // 50MB
    
    /**
     * 允许的文件类型
     */
    private String[] allowedTypes = {"text/html", "text/htm", "application/xhtml+xml"};
    
    /**
     * 允许的文件扩展名
     */
    private String[] allowedExtensions = {".html", ".htm"};
    
    /**
     * 格式化文件大小
     */
    public String formatFileSize(long bytes) {
        if (bytes == 0) return "0 Bytes";
        int k = 1024;
        String[] sizes = {"Bytes", "KB", "MB", "GB"};
        int i = (int) Math.floor(Math.log(bytes) / Math.log(k));
        return String.format("%.2f %s", bytes / Math.pow(k, i), sizes[i]);
    }
}
