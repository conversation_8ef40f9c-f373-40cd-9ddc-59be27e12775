package xiaowu.tgexplain.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 文件存储配置类
 * 
 * 负责管理HTML文件的本地存储配置，包括存储路径、文件命名规则等。
 * 遵循《阿里巴巴Java开发手册》的配置管理规范。
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "app.file-storage")
public class FileStorageConfig {
    
    /**
     * 文件存储根目录
     * 默认为项目根目录下的uploads文件夹
     */
    private String rootPath = "uploads";
    
    /**
     * 是否按日期创建子目录
     * 格式：yyyy/MM/dd
     */
    private boolean createDateDirectories = true;
    
    /**
     * 是否按群组创建子目录
     * 会对群组名进行安全处理，移除特殊字符
     */
    private boolean createGroupDirectories = true;
    
    /**
     * 文件名前缀
     * 用于标识文件来源
     */
    private String filePrefix = "tg_chat_";
    
    /**
     * 是否在文件名中包含时间戳
     * 格式：yyyyMMdd_HHmmss
     */
    private boolean includeTimestamp = true;
    
    /**
     * 是否保留原始文件名
     * 如果为true，会在生成的文件名后追加原始文件名
     */
    private boolean preserveOriginalName = true;
    
    /**
     * 文件名最大长度
     * 超过此长度的文件名会被截断
     */
    private int maxFileNameLength = 200;
    
    /**
     * 获取完整的存储根路径
     * 
     * @return 存储根路径的Path对象
     */
    public Path getStorageRootPath() {
        return Paths.get(rootPath).toAbsolutePath().normalize();
    }
    
    /**
     * 获取相对于项目根目录的存储路径
     * 
     * @return 相对路径字符串
     */
    public String getRelativeStoragePath() {
        Path currentDir = Paths.get("").toAbsolutePath();
        Path storagePath = getStorageRootPath();
        
        try {
            return currentDir.relativize(storagePath).toString();
        } catch (IllegalArgumentException e) {
            // 如果无法计算相对路径，返回绝对路径
            return storagePath.toString();
        }
    }
    
    /**
     * 清理文件名中的非法字符
     * 
     * @param fileName 原始文件名
     * @return 清理后的安全文件名
     */
    public String sanitizeFileName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return "unnamed_file";
        }
        
        // 移除或替换非法字符
        String sanitized = fileName
                .replaceAll("[\\\\/:*?\"<>|]", "_")  // Windows非法字符
                .replaceAll("[\\x00-\\x1f\\x7f]", "_")  // 控制字符
                .trim();
        
        // 确保文件名不为空
        if (sanitized.isEmpty()) {
            sanitized = "unnamed_file";
        }
        
        // 限制文件名长度
        if (sanitized.length() > maxFileNameLength) {
            String extension = "";
            int lastDotIndex = sanitized.lastIndexOf('.');
            if (lastDotIndex > 0) {
                extension = sanitized.substring(lastDotIndex);
                sanitized = sanitized.substring(0, lastDotIndex);
            }
            
            int maxNameLength = maxFileNameLength - extension.length();
            if (maxNameLength > 0) {
                sanitized = sanitized.substring(0, Math.min(sanitized.length(), maxNameLength));
            }
            sanitized += extension;
        }
        
        return sanitized;
    }
    
    /**
     * 清理群组名，使其适合作为目录名
     * 
     * @param groupName 原始群组名
     * @return 清理后的安全目录名
     */
    public String sanitizeGroupName(String groupName) {
        if (groupName == null || groupName.trim().isEmpty()) {
            return "unknown_group";
        }
        
        return sanitizeFileName(groupName)
                .replaceAll("\\.", "_")  // 替换点号，避免隐藏目录
                .toLowerCase();  // 转为小写，保持一致性
    }
}
