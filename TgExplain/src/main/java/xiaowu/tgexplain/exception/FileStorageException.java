package xiaowu.tgexplain.exception;

/**
 * 文件存储异常类
 * 
 * 用于处理文件存储过程中发生的各种异常情况。
 * 遵循《阿里巴巴Java开发手册》的异常处理规范，提供具体的异常类型。
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
public class FileStorageException extends Exception {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 构造函数
     * 
     * @param message 异常消息
     */
    public FileStorageException(String message) {
        super(message);
    }
    
    /**
     * 构造函数
     * 
     * @param message 异常消息
     * @param cause 原因异常
     */
    public FileStorageException(String message, Throwable cause) {
        super(message, cause);
    }
    
    /**
     * 构造函数
     * 
     * @param cause 原因异常
     */
    public FileStorageException(Throwable cause) {
        super(cause);
    }
}
