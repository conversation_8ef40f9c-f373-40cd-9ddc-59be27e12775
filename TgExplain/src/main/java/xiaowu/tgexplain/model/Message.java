package xiaowu.tgexplain.model;

import lombok.Data;
import java.util.List;

@Data
public class Message {
    private String id;
    private String type; // "service", "default"
    private String dateDetails; // For service messages
    private boolean isJoined; // To handle messages without a userpic

    // For default messages
    private String time;
    private String fromName;
    private String text;
    private String timestamp;
    private String userPicColorClass; // e.g., "userpic8"
    private String userInitial;

    // For special content
    private String replyToMessageId;
    private String imageUrl;
    private String thumbnailUrl;
    private Message forwardedMessage; // For forwarded content
    private List<Reaction> reactions;

    // For pinned service messages
    private String pinnedMessageId;
    private String pinnedMessageText;
}