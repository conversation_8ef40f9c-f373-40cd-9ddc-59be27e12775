package xiaowu.tgexplain.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import xiaowu.tgexplain.entity.MessageEntity;

import java.util.List;

@Repository
public interface MessageRepository extends JpaRepository<MessageEntity, Long> {

    /**
     * 检查具有给定业务ID的消息是否存在
     * @param businessId 业务ID
     * @return 如果存在则返回 true, 否则返回 false
     */
    boolean existsByBusinessId(String businessId);

    /**
     * 查找所有不同的群组名称
     * @return 群组名称列表
     */
    @Query("SELECT DISTINCT m.groupName FROM MessageEntity m ORDER BY m.groupName")
    List<String> findDistinctGroupNames();

    /**
     * 根据群组名称查找所有消息，并按时间升序排序
     * @param groupName 群组名称
     * @return 消息实体列表
     */
    List<MessageEntity> findByGroupNameOrderByMessageTimeAsc(String groupName);
}
