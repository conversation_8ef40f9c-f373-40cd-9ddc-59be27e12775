package xiaowu.tgexplain.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import xiaowu.tgexplain.dto.ChatMessageDto;
import xiaowu.tgexplain.entity.MessageEntity;
import xiaowu.tgexplain.service.DatabaseStorageService;

import java.io.IOException;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class ChatController {

    private final DatabaseStorageService storageService;
    private static final DateTimeFormatter DTO_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @PostMapping("/upload")
    public ResponseEntity<Map<String,Object>> uploadAndProcessFile(@RequestParam("file") List<MultipartFile> files) {
       if(files == null || files.isEmpty() || files.stream().allMatch(MultipartFile :: isEmpty))
       {
           return ResponseEntity.badRequest().body(Map.of("error","上传文件不能为空"));
       }
       List<String> successfulUploads = new ArrayList<>();
       Map<String,String> failedUploads = new HashMap<>();
       for(MultipartFile file : files)
       {
           if(file.isEmpty()){
               continue;
           }
           try{
               String result = storageService.processAndSaveHtml(file);
               successfulUploads.add("file" + file.getOriginalFilename() + "处理成功: " + result);
           }catch (IOException e){
               failedUploads.put(file.getOriginalFilename(),"文件处理失败: "+ e.getMessage());
           }
       }
        Map<String, Object> response = new HashMap<>();
        response.put("message", "文件上传处理完成");
        response.put("successfulFiles", successfulUploads);
        response.put("failedFiles", failedUploads);

        // 5. Determine the overall status
        if (!failedUploads.isEmpty() && successfulUploads.isEmpty()) {
            // All files failed
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        } else if (!failedUploads.isEmpty()) {
            // Partial success
            return ResponseEntity.status(HttpStatus.MULTI_STATUS).body(response);
        } else {
            // All files succeeded
            return ResponseEntity.ok(response);
        }
    }

    @GetMapping("/groups")
    public ResponseEntity<List<String>> getGroupNames() {
        List<String> groupNames = storageService.getDistinctGroupNames();
        return ResponseEntity.ok(groupNames);
    }

    @GetMapping("/messages/{groupName}")
    public ResponseEntity<List<ChatMessageDto>> getMessages(@PathVariable String groupName) {
        List<MessageEntity> messages = storageService.getMessagesByGroupName(groupName);
        List<ChatMessageDto> dtos = messages.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
        return ResponseEntity.ok(dtos);
    }

    private ChatMessageDto convertToDto(MessageEntity entity) {
        ChatMessageDto dto = new ChatMessageDto();
        dto.setId(entity.getId());
        dto.setGroupName(entity.getGroupName());
        dto.setSenderName(entity.getSenderName());
        dto.setMessageContent(entity.getMessageContent());
        if (entity.getMessageTime() != null) {
            dto.setMessageTime(entity.getMessageTime().format(DTO_DATE_FORMATTER));
        }
        dto.setReply(entity.isReply());
        dto.setRepliedMessageContent(entity.getRepliedMessageContent());
        dto.setRepliedToUser(entity.getRepliedToUser());
        dto.setImageUrl(entity.getImageUrl());
        return dto;
    }
}
