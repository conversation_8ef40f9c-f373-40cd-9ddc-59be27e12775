package xiaowu.tgexplain.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import xiaowu.tgexplain.service.FileStorageService;

import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;

/**
 * 文件访问控制器
 * 
 * 提供HTML文件的下载和访问功能。
 * 遵循《阿里巴巴Java开发手册》的RESTful API设计规范。
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/files")
@RequiredArgsConstructor
public class FileController {
    
    private final FileStorageService fileStorageService;
    
    /**
     * 下载HTML文件
     * 
     * @param relativePath 文件相对路径
     * @return 文件资源响应
     */
    @GetMapping("/download/**")
    public ResponseEntity<Resource> downloadFile(
            @RequestParam(name = "path", required = true) String relativePath) {
        
        try {
            // 验证文件路径
            if (relativePath == null || relativePath.trim().isEmpty()) {
                log.warn("文件路径为空");
                return ResponseEntity.badRequest().build();
            }
            
            // 安全检查：防止路径遍历攻击
            if (relativePath.contains("..") || relativePath.contains("\\")) {
                log.warn("检测到不安全的文件路径: {}", relativePath);
                return ResponseEntity.badRequest().build();
            }
            
            // 检查文件是否存在
            if (!fileStorageService.fileExists(relativePath)) {
                log.warn("文件不存在: {}", relativePath);
                return ResponseEntity.notFound().build();
            }
            
            // 获取文件路径
            Path filePath = fileStorageService.getAbsolutePath(relativePath);
            Resource resource = new UrlResource(filePath.toUri());
            
            if (!resource.exists() || !resource.isReadable()) {
                log.warn("文件不可读: {}", relativePath);
                return ResponseEntity.notFound().build();
            }
            
            // 获取文件名
            String fileName = filePath.getFileName().toString();
            
            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, 
                    "attachment; filename=\"" + fileName + "\"");
            headers.add(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_HTML_VALUE);
            
            log.info("文件下载请求: {} -> {}", relativePath, fileName);
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(resource);
                    
        } catch (MalformedURLException e) {
            log.error("文件路径格式错误: {}", relativePath, e);
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            log.error("文件下载失败: {}", relativePath, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 获取文件信息
     * 
     * @param relativePath 文件相对路径
     * @return 文件信息
     */
    @GetMapping("/info")
    public ResponseEntity<FileInfo> getFileInfo(
            @RequestParam(name = "path", required = true) String relativePath) {
        
        try {
            // 验证文件路径
            if (relativePath == null || relativePath.trim().isEmpty()) {
                return ResponseEntity.badRequest().build();
            }
            
            // 安全检查
            if (relativePath.contains("..") || relativePath.contains("\\")) {
                return ResponseEntity.badRequest().build();
            }
            
            // 检查文件是否存在
            if (!fileStorageService.fileExists(relativePath)) {
                return ResponseEntity.notFound().build();
            }
            
            // 获取文件信息
            Path filePath = fileStorageService.getAbsolutePath(relativePath);
            
            FileInfo fileInfo = new FileInfo();
            fileInfo.setPath(relativePath);
            fileInfo.setName(filePath.getFileName().toString());
            fileInfo.setSize(Files.size(filePath));
            fileInfo.setLastModified(Files.getLastModifiedTime(filePath).toMillis());
            fileInfo.setExists(true);
            
            return ResponseEntity.ok(fileInfo);
            
        } catch (IOException e) {
            log.error("获取文件信息失败: {}", relativePath, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 检查文件是否存在
     * 
     * @param relativePath 文件相对路径
     * @return 文件存在状态
     */
    @GetMapping("/exists")
    public ResponseEntity<Boolean> fileExists(
            @RequestParam(name = "path", required = true) String relativePath) {
        
        // 验证文件路径
        if (relativePath == null || relativePath.trim().isEmpty()) {
            return ResponseEntity.badRequest().build();
        }
        
        // 安全检查
        if (relativePath.contains("..") || relativePath.contains("\\")) {
            return ResponseEntity.badRequest().build();
        }
        
        boolean exists = fileStorageService.fileExists(relativePath);
        return ResponseEntity.ok(exists);
    }
    
    /**
     * 文件信息DTO
     */
    public static class FileInfo {
        private String path;
        private String name;
        private long size;
        private long lastModified;
        private boolean exists;
        
        // Getters and Setters
        public String getPath() { return path; }
        public void setPath(String path) { this.path = path; }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public long getSize() { return size; }
        public void setSize(long size) { this.size = size; }
        
        public long getLastModified() { return lastModified; }
        public void setLastModified(long lastModified) { this.lastModified = lastModified; }
        
        public boolean isExists() { return exists; }
        public void setExists(boolean exists) { this.exists = exists; }
    }
}
