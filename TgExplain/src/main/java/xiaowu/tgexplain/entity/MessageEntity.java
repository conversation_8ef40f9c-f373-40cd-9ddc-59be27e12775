package xiaowu.tgexplain.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

@Entity
@Table(name = "chat_messages")
@Data
public class MessageEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "主键ID")
    private Long id;

    @Column(name = "业务ID", unique = true)
    private String businessId;

    @Column(name = "群组名称", length = 500)
    private String groupName;

    @Column(name = "发送者", length = 500)
    private String senderName;

    @Lob
    @Column(name = "消息内容", columnDefinition = "TEXT")
    private String messageContent;

    @Column(name = "发送时间")
    private LocalDateTime messageTime;

    @Column(name = "是否为回复")
    private boolean isReply;

    @Lob
    @Column(name = "回复的消息内容", columnDefinition = "TEXT")
    private String repliedMessageContent;

    @Column(name = "被回复者", length = 500)
    private String repliedToUser;

    @Lob
    @Column(name = "图片链接", columnDefinition = "TEXT")
    private String imageUrl;

    @Column(name = "HTML文件路径", length = 1000)
    private String htmlFilePath;
}
