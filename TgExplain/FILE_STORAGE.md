# TgExplain 文件存储功能说明

## 概述

TgExplain项目现已支持HTML文件的本地存储功能。当用户上传HTML聊天记录文件时，系统不仅会解析内容存储到数据库，还会将原始HTML文件保存到服务器本地磁盘，并在数据库中记录文件路径。

## 功能特性

### 1. 智能文件存储
- **自动目录创建**：按日期和群组自动创建目录结构
- **唯一文件名**：使用时间戳+UUID确保文件名唯一性
- **原始文件名保留**：可选择保留原始文件名
- **安全文件名处理**：自动清理文件名中的非法字符

### 2. 目录结构
```
uploads/
├── 2025/
│   ├── 01/
│   │   ├── 01/
│   │   │   ├── test_group/
│   │   │   │   ├── tg_chat_20250101_143022_a1b2c3d4_original_file.html
│   │   │   │   └── tg_chat_20250101_143055_e5f6g7h8_another_file.html
│   │   │   └── another_group/
│   │   │       └── tg_chat_20250101_144012_i9j0k1l2_group_chat.html
│   │   └── 02/
│   └── 02/
```

### 3. 配置选项
通过 `application.yml` 可配置以下选项：

```yaml
app:
  file-storage:
    root-path: uploads                    # 存储根目录
    create-date-directories: true         # 按日期创建目录
    create-group-directories: true        # 按群组创建目录
    file-prefix: "tg_chat_"              # 文件名前缀
    include-timestamp: true               # 包含时间戳
    preserve-original-name: true          # 保留原始文件名
    max-file-name-length: 200            # 文件名最大长度
```

## 核心组件

### 1. FileStorageConfig
文件存储配置类，管理所有存储相关的配置参数。

### 2. FileStorageService
文件存储服务类，提供以下功能：
- `saveHtmlFile()` - 保存HTML文件
- `fileExists()` - 检查文件是否存在
- `deleteFile()` - 删除文件
- `getAbsolutePath()` - 获取文件绝对路径

### 3. DatabaseStorageService
数据库存储服务，已集成文件存储功能：
- 在解析HTML内容的同时保存原始文件
- 在数据库中记录文件路径
- 提供事务一致性和异常回滚

### 4. FileController
文件访问控制器，提供文件下载和信息查询API：
- `GET /api/files/download?path=xxx` - 下载文件
- `GET /api/files/info?path=xxx` - 获取文件信息
- `GET /api/files/exists?path=xxx` - 检查文件是否存在

## 安全特性

### 1. 路径安全
- 防止路径遍历攻击（../ 和 \）
- 文件名安全处理，移除非法字符
- 相对路径存储，避免绝对路径泄露

### 2. 文件验证
- 严格的文件类型检查（仅支持.html/.htm）
- 文件大小限制
- 空文件检测

### 3. 异常处理
- 完整的异常处理机制
- 事务回滚时自动清理已保存的文件
- 详细的错误日志记录

## 使用示例

### 1. 上传文件
```bash
curl -X POST \
  http://localhost:8080/api/upload \
  -F "file=@chat_export.html"
```

### 2. 下载文件
```bash
curl -X GET \
  "http://localhost:8080/api/files/download?path=2025/01/01/test_group/tg_chat_20250101_143022_a1b2c3d4_chat_export.html" \
  -o downloaded_file.html
```

### 3. 检查文件信息
```bash
curl -X GET \
  "http://localhost:8080/api/files/info?path=2025/01/01/test_group/tg_chat_20250101_143022_a1b2c3d4_chat_export.html"
```

## 数据库集成

### MessageEntity 更新
`htmlFilePath` 字段现在存储实际的文件相对路径：
```java
@Column(name = "HTML文件路径", length = 1000)
private String htmlFilePath;
```

### ChatMessageDto 更新
前端DTO也包含文件路径信息：
```java
private String htmlFilePath;
```

## 错误处理

### 常见异常
- `FileStorageException` - 文件存储相关异常
- `IOException` - 文件I/O异常
- `IllegalArgumentException` - 参数验证异常

### 异常处理策略
1. **事务一致性**：数据库操作失败时自动清理已保存的文件
2. **详细日志**：记录所有文件操作的详细信息
3. **优雅降级**：文件存储失败不影响数据解析功能

## 性能考虑

### 1. 存储优化
- 按日期和群组分目录，避免单目录文件过多
- 使用相对路径，减少存储空间
- 文件名长度限制，避免文件系统限制

### 2. 并发安全
- 使用UUID确保文件名唯一性
- 原子性文件操作
- 线程安全的服务设计

## 维护建议

### 1. 定期清理
建议定期清理过期的HTML文件，可以通过以下方式：
- 设置文件保留期限
- 实现定时清理任务
- 提供手动清理接口

### 2. 备份策略
- 定期备份uploads目录
- 数据库备份时同步备份文件
- 考虑使用云存储作为备份

### 3. 监控告警
- 监控磁盘空间使用情况
- 文件存储失败率监控
- 异常情况告警机制

## 扩展功能

### 未来可考虑的功能
1. **云存储支持**：集成AWS S3、阿里云OSS等
2. **文件压缩**：自动压缩存储的HTML文件
3. **文件去重**：基于内容哈希的文件去重
4. **访问控制**：基于用户权限的文件访问控制
5. **文件预览**：在线预览HTML文件内容
