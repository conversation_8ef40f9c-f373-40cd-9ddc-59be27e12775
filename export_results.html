<!DOCTYPE html>
<html>

 <head>

  <meta charset="utf-8"/>
<title>Exported Data</title>
  <meta content="width=device-width, initial-scale=1.0" name="viewport"/>

  <link href="css/style.css" rel="stylesheet"/>

  <script src="js/script.js" type="text/javascript">

  </script>

 </head>

 <body onload="CheckLocation();">

  <div class="page_wrap">

   <div class="page_header">

    <div class="content">

     <div class="text bold">
Exported Data
     </div>

    </div>

   </div>

   <div class="page_body">

    <div class="personal_info clearfix">

     <div class="pull_right userpic_wrap">

      <div class="userpic userpic8" style="width: 90px; height: 90px">

       <div class="initials" style="line-height: 90px">
绿
       </div>

      </div>

     </div>

     <div class="rows names">

      <div class="row">

       <div class="label details">
First name
       </div>

       <div class="value bold">
绿茶 备用号515
       </div>

      </div>

     </div>

     <div class="rows info">

      <div class="row">

       <div class="label details">
Phone number
       </div>

       <div class="value bold">
+855 88 338 0786
       </div>

      </div>

      <div class="row">

       <div class="label details">
Username
       </div>

       <div class="value bold">
@lvcha515
       </div>

      </div>

     </div>

     <div class="rows bio">

     </div>

    </div>

    <div class="sections with_divider">

     <a class="section block_link chats" href="lists/chats.html#allow_back">

      <div class="counter details">
34
      </div>

      <div class="label bold">
Chats
      </div>

     </a>

     <a class="section block_link frequent" href="lists/frequent.html#allow_back">

      <div class="counter details">
20
      </div>

      <div class="label bold">
Frequent contacts
      </div>

     </a>

     <a class="section block_link stories" href="lists/stories.html#allow_back">

      <div class="counter details">
0
      </div>

      <div class="label bold">
Stories archive
      </div>

     </a>

     <a class="section block_link sessions" href="lists/sessions.html#allow_back">

      <div class="counter details">
3
      </div>

      <div class="label bold">
Sessions
      </div>

     </a>

     <a class="section block_link other" href="lists/other_data.json#allow_back">

      <div class="counter details">
1
      </div>

      <div class="label bold">
Other data
      </div>

     </a>

    </div>

    <div class="page_about details with_divider">
这里是您请求的全部数据。请记住：Telegram 并不将您的数据用于广告定位也不出售您的数据。Telegram 只储存用于提供安全且功能丰富的云服务所需的信息。<br><br>您可以在 Telegram 的移动应用的 设置 &gt; 隐私和安全 中查找相关设置。
    </div>

   </div>

  </div>

 </body>

</html>
